require('dotenv').config();
const { getAuthToken, getGroupMembers, getGroupByName } = require('./services/graph');
const logger = require('./services/logger');
// const createCsvWriter = require('csv-writer').createObjectCsvWriter;

/**
 * Check members of a group by group ID or name
 * @param {string} groupIdentifier - Group ID or group name
 * @param {boolean} isGroupName - Whether the identifier is a group name (default: false)
 * @returns {Object} - Group members information
 */
async function checkGroupMembers(groupIdentifier, isGroupName = false) {
    try {
        console.log(`\n🔍 Checking members for group: ${groupIdentifier}`);
        console.log('='.repeat(60));
        
        // Initialize database connection
        const database = require('./services/database.service');
        await database.initialize();
        
        let groupId = groupIdentifier;
        let groupInfo = null;

        // If it's a group name, find the group ID first
        if (isGroupName) {
            console.log('📋 Step 1: Looking up group by name...');
            groupInfo = await getGroupByName(groupIdentifier);
            if (!groupInfo) {
                return {
                    success: false,
                    groupFound: false,
                    groupIdentifier: groupIdentifier,
                    message: 'Group not found in Azure AD'
                };
            }
            groupId = groupInfo.id;
            console.log(`   Found group: ${groupInfo.displayName} (${groupInfo.id})`);
        }

        // Get group members
        console.log('📋 Step 2: Fetching group members...');
        const members = await getGroupMembers(groupId);
        
        if (!members || members.length === 0) {
            console.log('   No members found in this group');
            return {
                success: true,
                groupFound: true,
                groupId: groupId,
                groupName: groupInfo ? groupInfo.displayName : 'Unknown',
                members: [],
                memberCount: 0
            };
        }

        console.log(`   Found ${members.length} members`);

        // Categorize members
        const categorizedMembers = {
            users: [],
            groups: [],
            other: []
        }; 

        members.forEach(member => {
            if (member['@odata.type'] === '#microsoft.graph.user') {
                categorizedMembers.users.push({
                    id: member.id,
                    displayName: member.displayName,
                    email: member.mail || member.userPrincipalName,
                    userPrincipalName: member.userPrincipalName,
                    accountEnabled: member.accountEnabled,
                    userType: member.userType,
                    type: 'User'
                });
            } else if (member['@odata.type'] === '#microsoft.graph.group') {
                categorizedMembers.groups.push({
                    id: member.id,
                    displayName: member.displayName,
                    description: member.description,
                    groupTypes: member.groupTypes,
                    type: 'Group'
                });
            } else {
                categorizedMembers.other.push({
                    id: member.id,
                    displayName: member.displayName,
                    type: member['@odata.type'] || 'Other'
                });
            }
        });

        // Display results
        console.log('\n📊 Member Summary:');
        console.log(`   Total Members: ${members.length}`);
        console.log(`   Users: ${categorizedMembers.users.length}`);
        console.log(`   Groups: ${categorizedMembers.groups.length}`);
        console.log(`   Other: ${categorizedMembers.other.length}`);

        // Display users
        if (categorizedMembers.users.length > 0) {
            console.log('\n👥 User Members:');
            categorizedMembers.users.forEach((user, index) => {
                console.log(`   ${index + 1}. ${user.displayName}`);
                console.log(`      Email: ${user.email || 'N/A'}`);
                console.log(`      UPN: ${user.userPrincipalName || 'N/A'}`);
                console.log(`      Account Enabled: ${user.accountEnabled !== false}`);
                console.log(`      User Type: ${user.userType || 'Member'}`);
                console.log('');
            });
        }

        // Display nested groups
        if (categorizedMembers.groups.length > 0) {
            console.log('\n🏢 Group Members (Nested Groups):');
            categorizedMembers.groups.forEach((group, index) => {
                console.log(`   ${index + 1}. ${group.displayName}`);
                console.log(`      Description: ${group.description || 'N/A'}`);
                console.log(`      Group Types: ${group.groupTypes ? group.groupTypes.join(', ') : 'N/A'}`);
                console.log('');
            });
        }

        const result = {
            success: true,
            groupFound: true,
            groupId: groupId,
            groupName: groupInfo ? groupInfo.displayName : 'Unknown',
            groupDescription: groupInfo ? groupInfo.description : 'N/A',
            members: members,
            categorizedMembers: categorizedMembers,
            memberCount: members.length,
            summary: {
                totalMembers: members.length,
                userMembers: categorizedMembers.users.length,
                groupMembers: categorizedMembers.groups.length,
                otherMembers: categorizedMembers.other.length
            }
        };

        console.log('\n✅ Group member check completed successfully!');
        console.log('='.repeat(60));
        
        return result;

    } catch (error) {
        console.error(`\n❌ Error checking group members: ${error.message}`);
        return {
            success: false,
            error: error.message,
            groupIdentifier: groupIdentifier
        };
    }
}

/**
 * Export group members to CSV
 * @param {Object} result - Result from checkGroupMembers
 * @param {string} outputPath - Output file path
 */
async function exportMembersToCSV(result, outputPath) {
    try {
        if (!result.success || !result.groupFound) {
            console.log('No data to export');
            return;
        }

        const allMembers = [
            ...result.categorizedMembers.users,
            ...result.categorizedMembers.groups,
            ...result.categorizedMembers.other
        ];

        const csvWriter = createCsvWriter({
            path: outputPath,
            header: [
                {id: 'displayName', title: 'Display Name'},
                {id: 'email', title: 'Email'},
                {id: 'userPrincipalName', title: 'User Principal Name'},
                {id: 'id', title: 'Object ID'},
                {id: 'type', title: 'Type'},
                {id: 'accountEnabled', title: 'Account Enabled'},
                {id: 'userType', title: 'User Type'},
                {id: 'description', title: 'Description'}
            ]
        });

        await csvWriter.writeRecords(allMembers);
        console.log(`\n📄 Group members exported to: ${outputPath}`);
        
    } catch (error) {
        console.error(`Error exporting to CSV: ${error.message}`);
    }
}

/**
 * Check multiple groups at once
 * @param {Array<string>} groupIdentifiers - Array of group IDs or names
 * @param {boolean} areGroupNames - Whether identifiers are group names
 */
async function checkMultipleGroups(groupIdentifiers, areGroupNames = false) {
    console.log(`\n🔍 Checking members for ${groupIdentifiers.length} groups...\n`);
    
    const results = [];
    
    for (let i = 0; i < groupIdentifiers.length; i++) {
        const identifier = groupIdentifiers[i];
        console.log(`[${i + 1}/${groupIdentifiers.length}] Processing: ${identifier}`);
        
        const result = await checkGroupMembers(identifier, areGroupNames);
        results.push(result);
        
        // Add delay between requests to avoid rate limiting
        if (i < groupIdentifiers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // Summary
    console.log('\n📊 Batch Processing Summary:');
    const successful = results.filter(r => r.success && r.groupFound).length;
    const notFound = results.filter(r => r.success && !r.groupFound).length;
    const errors = results.filter(r => !r.success).length;
    const totalMembers = results.filter(r => r.success).reduce((sum, r) => sum + (r.memberCount || 0), 0);
    
    console.log(`   Successfully processed: ${successful}`);
    console.log(`   Groups not found: ${notFound}`);
    console.log(`   Errors: ${errors}`);
    console.log(`   Total members across all groups: ${totalMembers}`);
    
    return results;
}

// CLI Usage
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('\n📖 Usage Examples:');
        console.log('node check-group-members.js 3f7f8bb8-1151-4e94-99c8-184433a891a2');
        console.log('node check-group-members.js --name "MATRIX_READ_OPEN"');
        console.log('node check-group-members.js --export group-id-here');
        console.log('node check-group-members.js --multiple group1,group2,group3');
        console.log('\n📝 Options:');
        console.log('--name : Search by group name instead of ID');
        console.log('--export : Export members to CSV file');
        console.log('--multiple : Check multiple groups (comma-separated)');
        process.exit(1);
    }

    try {
        const exportMode = args.includes('--export');
        const nameMode = args.includes('--name');
        const multipleMode = args.includes('--multiple');
        
        const identifiers = args.filter(arg => !arg.startsWith('--'));
        
        if (multipleMode) {
            // Multiple groups
            const groupList = identifiers[0].split(',').map(g => g.trim());
            await checkMultipleGroups(groupList, nameMode);
        } else {
            // Single group
            const result = await checkGroupMembers(identifiers[0], nameMode);
            
            if (exportMode && result.success) {
                const filename = `./members-${identifiers[0].replace(/[^a-zA-Z0-9]/g, '-')}.csv`;
                await exportMembersToCSV(result, filename);
            }
        }
        
    } catch (error) {
        console.error('❌ Script Error:', error.message);
        process.exit(1);
    }
}

// Export functions for use in other scripts
module.exports = {
    checkGroupMembers,
    exportMembersToCSV,
    checkMultipleGroups
};

// // Run if called directly
if (require.main === module) {
    main();
}
// checkGroupMembers(identifiers[0], nameMode).then(data=>console.log(data));