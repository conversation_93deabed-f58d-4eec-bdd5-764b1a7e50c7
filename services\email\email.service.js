const mailer = require("nodemailer");
const nunjucks = require("nunjucks");
const logger = require("../logger");

nunjucks.configure(__dirname + "/templates", {
  autoescape: true,
  noCache: process.env.NODE_ENV == "DEV" ? true : false,
});

/**
 * Create attachments array for nodemailer
 * based on the image file names array passed.
 * (names with file extension)
 *
 * The names (without file extension) specified
 * will be used as the cid to be used in the template.
 * @param {Array<string>} imageFileNames
 */
function createAttachments(imageFileNames) {
  return imageFileNames.map((name) => {
    let subdir = "images";
    if (name.includes(".pdf")) {
      subdir = "attachments";
    }
    return {
      filename: name,
      path: __dirname + `/${subdir}/${name}`,
      cid: name.split(".")[0],
    };
  });
}

async function sendEmail(data) {
  try {
    const transporter = mailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false, //true for 465, false for other ports
      logger: true,
      debug: false,
      auth: {
        user: "<EMAIL>",
        pass: "oPi2H6UdF3Ge2ehFCLLVt8KA8LCBtDmE",
      },
      tls: {
        // do not fail on invalid certs
        rejectUnauthorized: false,
      },
    });
    let emailMessage;
    let mailOpts = {
      from: process.env.SYSTEM_MAILID,
      to: data.recepientEmail,
      subject: data.subject || `Bayaan Access`,
      attachments: [],
    };

    switch (data.emailType) {
      case "PLATFORM_WELCOME_EMAIL":
        emailMessage = nunjucks.render("welcome-email.njk", data);
        let fileAttachments = [
          "Bayaan_NDA_English.pdf",
          "Bayaan_NDA_Arabic.pdf",
        ];
        if (data.recipientRole == "SUPERUSER") {
          fileAttachments = [
            "Bayaan_Superuser_NDA_Arabic.pdf",
            "Bayaan_Superuser_NDA_English.pdf",
            "Bayaan_Superuser_Roles_and_Responsibilites_Arabic.pdf",
            "Bayaan_Superuser_Roles_and_Responsibilites_English.pdf",
          ];
        }
        const emailAssets = [
          "invite-banner.png",
          "arrow.png",
          "lap.png",
          "mobile.png",
          "apple-icon.png",
          "qr-im.png",
          "or-arabic.png",
          "lock.png",
          "or.png",
          "android-icon.png",
        ];
        const attachments = emailAssets.concat(fileAttachments);
        mailOpts.attachments = createAttachments(attachments);
        break;
      default:
        logger.error(`Invalid emailType: ${data.emailType}`);
        break;
    }

    mailOpts.html = emailMessage;
    const result = await transporter.sendMail(mailOpts);
    logger.info(
      `Email sent successfully to ${data.recepientEmail} \n${result.response}`
    );
  } catch (error) {
    throw error;
  }
}

module.exports = {
  sendEmail,
};
