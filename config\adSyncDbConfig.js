/**
 * Database configuration for AD Sync database
 */
require('dotenv').config();

module.exports = {
    host: process.env.AD_SYNC_DB_HOST || '*************',
    port: process.env.AD_SYNC_DB_PORT || 5432,
    database: process.env.AD_SYNC_DB_NAME || 'byn_web_portaldata_stg',
    username: process.env.AD_SYNC_DB_USER || 'genai',
    password: process.env.AD_SYNC_DB_PASSWORD || 'n7Kaj2WKiV0XVqlyaTp5ED',
    dialect: 'postgres',
    logging: false,
    pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
    },
    dialectOptions: {
        ssl: {
            require: true,
            rejectUnauthorized: false // Set to true if you have proper SSL certificates
        }
    }
};

