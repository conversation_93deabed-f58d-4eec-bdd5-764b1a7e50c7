#!/usr/bin/env node

/**
 * Standalone script to list groups of a user from Microsoft Graph API
 * 
 * Usage:
 *   node list-user-groups.js --user-id <user-id>
 *   node list-user-groups.js --email <user-email>
 *   node list-user-groups.js --user-id <user-id> --names-only
 *   node list-user-groups.js --email <user-email> --names-only
 * 
 * Environment variables required:
 *   USER_CLIENT_ID - Azure AD application client ID
 *   USER_CLIENT_SECRET - Azure AD application client secret
 *   USER_TENANT_ID - Azure AD tenant ID
 *   USER_GRAPH_ENDPOINT - Microsoft Graph API endpoint (usually https://graph.microsoft.com)
 */

const { program } = require('commander');
const msal = require('@azure/msal-node');
const axios = require('axios');
require('dotenv').config();

// Configuration for MSAL
const msalConfig = {
    auth: {
        clientId: process.env.USER_CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.USER_TENANT_ID}`,
        clientSecret: process.env.USER_CLIENT_SECRET,
    }
};

const tokenRequest = {
    scopes: [`${process.env.USER_GRAPH_ENDPOINT}/.default`],
};

// Initialize MSAL client
let cca;
try {
    cca = new msal.ConfidentialClientApplication(msalConfig);
} catch (error) {
    console.error('Failed to initialize MSAL client:', error.message);
    process.exit(1);
}

/**
 * Get authentication token from Microsoft Graph API
 */
async function getAuthToken() {
    try {
        const authData = await cca.acquireTokenByClientCredential(tokenRequest);
        return authData.accessToken;
    } catch (error) {
        console.error('Failed to acquire authentication token:', error.message);
        throw error;
    }
}

/**
 * Get user ID from email address
 */
async function getUserIdFromEmail(email) {
    try {
        const authToken = await getAuthToken();
        
        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        console.log(`Looking up user ID for email: ${email}`);
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/users/${email}`,
            options
        );

        return response.data.id;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            throw new Error(`User with email ${email} not found`);
        }
        console.error(`Error looking up user ID for email ${email}:`, error.message);
        throw error;
    }
}

/**
 * Get groups for a specific user
 */
async function getUserGroups(userId) {
    try {
        console.log(`Fetching groups for user ID: ${userId}`);

        const authToken = await getAuthToken();
        
        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/users/${userId}/memberOf`,
            options
        );

        return response.data.value;
    } catch (error) {
        console.error(`Error fetching groups for user ${userId}:`, error.message);
        throw error;
    }
}

/**
 * Format and display group information
 */
function displayGroups(groups, userId, showNamesOnly = false) {
    console.log('\n' + '='.repeat(60));
    console.log(`GROUPS FOR USER: ${userId}`);
    console.log('='.repeat(60));
    
    if (groups.length === 0) {
        console.log('No groups found for this user.');
        return;
    }

    console.log(`Total groups: ${groups.length}\n`);
    
    if (showNamesOnly) {
        console.log('GROUP NAMES:');
        console.log('-'.repeat(40));
        groups.forEach((group, index) => {
            console.log(`${index + 1}. ${group.displayName || 'N/A'}`);
        });
        console.log('-'.repeat(40));
        return;
    }
    
    groups.forEach((group, index) => {
        console.log(`${index + 1}. Group Name: ${group.displayName || 'N/A'}`);
        console.log(`   Group ID: ${group.id}`);
        console.log(`   Group Type: ${group['@odata.type'] || 'N/A'}`);
        console.log(`   Description: ${group.description || 'N/A'}`);
        console.log(`   Security Enabled: ${group.securityEnabled || 'N/A'}`);
        console.log(`   Mail Enabled: ${group.mailEnabled || 'N/A'}`);
        console.log(`   Created: ${group.createdDateTime || 'N/A'}`);
        console.log('-'.repeat(40));
    });
}

/**
 * Validate required environment variables
 */
function validateEnvironment() {
    const requiredVars = [
        'USER_CLIENT_ID',
        'USER_CLIENT_SECRET', 
        'USER_TENANT_ID',
        'USER_GRAPH_ENDPOINT'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`  - ${varName}`);
        });
        console.error('\nPlease set these environment variables in your .env file or environment.');
        process.exit(1);
    }
}

/**
 * Main function
 */
async function main() {
    try {
        // Validate environment variables
        validateEnvironment();

        // Parse command line arguments
        program
            .option('--user-id <userId>', 'User ID to look up groups for')
            .option('--email <email>', 'User email to look up groups for')
            .option('--names-only', 'Show only group names (simplified output)')
            .parse();

        const options = program.opts();

        if (!options.userId && !options.email) {
            console.error('Error: Please provide either --user-id or --email');
            console.error('Usage:');
            console.error('  node list-user-groups.js --user-id <user-id>');
            console.error('  node list-user-groups.js --email <user-email>');
            console.error('  node list-user-groups.js --user-id <user-id> --names-only');
            console.error('  node list-user-groups.js --email <user-email> --names-only');
            process.exit(1);
        }

        let userId = options.userId;
        
        // If email is provided, get the user ID first
        if (options.email) {
            userId = await getUserIdFromEmail(options.email);
            console.log(`Found user ID: ${userId} for email: ${options.email}`);
        }

        // Get user groups
        const groups = await getUserGroups(userId);
        
        // Display results
        displayGroups(groups, userId, options.namesOnly);

    } catch (error) {
        console.error('Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    getUserGroups,
    getUserIdFromEmail,
    getAuthToken
};
