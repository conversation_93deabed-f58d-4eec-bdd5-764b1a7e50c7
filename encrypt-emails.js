require('dotenv').config();
const { encryptEmail, decryptEmail } = require('./encryption-helper');

// List of emails from the image
// const emails = [
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
// ];

// const emails = [
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//    "<EMAIL>",
//     "<EMAIL>",   
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>",
//     "<EMAIL>"
// ]



const emails = ["<EMAIL>"]
console.log('Encrypting emails...\n');
console.log('Original Email | Encrypted Email\n');
console.log('-'.repeat(80));

emails.forEach(email => {
    const encrypted = encryptEmail(email);
    console.log(`${email} | ${encrypted}`);
});

console.log('\n\n=== Encrypted emails only ===\n');
emails.forEach(email => {
    console.log(encryptEmail(email));
});

// "1a6f8c098abb8f3548f7ccbfad2cec66195d9223437fb032d449255ac479be96",
// "6ba4affb3f8e6d8d70077146d4550d065a77b4bea8b0bf6dab069e86e169d619",
// "d9645ab755c2c2c256dccf21bc49bba3c161c3ca24c4cad79940b6d4e7658c03",
// "a665826883a32fff8f0eb37fe91b1dc258c309643815c7365f26bcaf776cb126",
// "8e5d013f29781e95c767197c47463744b6a423389ddb2621b9b0a079fe84408d",
// "953b24d2946f9bc7651209563c2779b36a6797a1f8e40a30495e6d87fa9f5599"

// "0792ec60d525a914b1341b5d6e81ed8b3cd40d92b9dcaea58d48fcef991f5dbd",
// "6484b7fc5e3913e4dcb6d4cb3d787838522e6935d65d74b1c96dc94dc97ebb0a",
// "c6912dc95f15caef6bd0a92131e0d9e565f44363d9b9c935379ba375105db24c",
// "f20f65b0ef89bd745cfea88ff090cebe477a86b3ed8c6fdaef4b845cb3ab9b6e",
// "e8b602196e1a23d281736a76f202660774c3b05deec95892de18957dcd3c2213",
// "e4d3ed7b10c8f223d2a91645d0707dc353186a1e610c9352838ec5a2b78df58c",
// "217366986db04c6af3f19275435e1c8fe0da29ad427f01ad9fc385cb79f4d4dccdbf9e84618d55932e1c55d71d57a637",
// "08dd49b3a99d5b487e75df98be76f8342da49b15727f9f42421415c45c9dcf54",
// "c0728ffb29924286abe7a68dc15a7985e2656bc3d1d6e6955f38863a9e1c54ca",
// "af165c092777b08ffa3e2bab9c6cb45191ad507296e7264fd86928d45e975475",
// "8a5ea808382848d10a0acbfc01c575790854ac1c0ac68235e468465a3080254f",
// "4f0261cf6a6f92f18ab72603c768fe121046866fc39933594e243130a32df4f0",
// "e3595d4fa93ab48f0cbb3c5de74a5ad59809d907e5281a902cda76cad8cb17bc",
// "b9b56e7f935cbe594d971a751bc76c260d9f9a9f2616e469999af676fc7d84ad",
// "0b59a09df045428d625f26b78eb5dea713734f501de594a29a7d3516edc0b978",
// "eee838b3f7d685a69ed751a34a343b4b98dd23169db48631c9c49ce4bb7938f7"