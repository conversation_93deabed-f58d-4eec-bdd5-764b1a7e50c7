const oracledb = require("oracledb");
const uuid = require("uuid");
const crypto = require("crypto");
const dbConfig = {
  user: "SC_ENT_DWH_STG", // replace with your Oracle DB username
  password: "Welcome1", // replace with your Oracle DB password
  connectString: "*********:1521/byngwprd", // replace with your Oracle DB connection string (e.g., host:port/sid)
};

function encryptEmail(email) {
  const algorithm = "aes-256-cbc"; // AES encryption algorithm
  const ivLength = 16; // Initialization vector length
  const iv = Buffer.alloc(16, 0);

  const cipher = crypto.createCipheriv(
    algorithm,
    "ed9f5d9c975e9aa9d14bf50fcf08427e",
    iv
  ); // Use the fixed IV
  let encrypted = cipher.update(email, "utf8", "hex");
  encrypted += cipher.final("hex");
  return encrypted;
}

function selectRandomSensitiveAccess(sensitiveList) {
  // Randomly choose how many entries to select (between 1 and the length of the list)
  const numSelections = Math.floor(Math.random() * sensitiveList.length) + 1;

  // Shuffle the array and select the first 'numSelections' elements
  const shuffledList = sensitiveList.sort(() => 0.5 - Math.random());

  // Return the randomly selected entries
  return shuffledList.slice(0, numSelections);
}

// Example usage

// Function to insert a user into the database
async function insertUser(userData) {
  let connection;

  try {
    // Get a connection to the database
    connection = await oracledb.getConnection(dbConfig);

    // SQL query to insert a new user
    const insertSql = `
        INSERT INTO IFP_FLOW_USERS_V2 (ID, NAME, EMAIL, PHONE_NUMBER, ROLE, ENTITY_ID, STATUS, ACTIVATION_FLAG, DESIGNATION) 
        VALUES (:ID, :NAME, :EMAIL, :PHONE_NUMBER, :ROLE, :ENTITY_ID, :STATUS, :ACTIVATION_FLAG, :DESIGNATION)
      `;

    // Execute the SQL query with user data
    const result = await connection.execute(
      insertSql,
      {
        ID: userData.ID,
        NAME: userData.NAME,
        EMAIL: userData.EMAIL,
        PHONE_NUMBER: userData.PHONE_NUMBER || null,
        ROLE: userData.ROLE,
        ENTITY_ID: userData.ENTITY_ID,
        STATUS: userData.STATUS,
        ACTIVATION_FLAG: userData.ACTIVATION_FLAG,
        DESIGNATION: userData.DESIGNATION,
      },
      { autoCommit: true }
    );

    console.log(
      "User inserted successfully. Rows affected:",
      result.rowsAffected
    );
  } catch (err) {
    console.error("Error inserting user:", err);
  } finally {
    // Close the connection
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing connection:", err);
      }
    }
  }
}

async function insertRequest(requestData) {
  let connection;

  try {
    // Get a connection to the database
    connection = await oracledb.getConnection(dbConfig);

    // SQL query to insert a new request
    const insertSql = `
        INSERT INTO IFP_USER_ACCESS_REQUEST (REQUEST_ID, STATUS, USER_ID, ENTRA_ID_ASSIGN_STATUS)
        VALUES (:REQUEST_ID, :STATUS, :USER_ID, :ENTRA_ID_ASSIGN_STATUS)
      `;

    // Generate the required fields for the request
    const requestId = uuid.v4(); // Generate a new REQUEST_ID

    // Execute the SQL query with the request data
    const result = await connection.execute(
      insertSql,
      {
        REQUEST_ID: requestId,
        STATUS: requestData.STATUS,
        USER_ID: requestData.USER_ID,
        ENTRA_ID_ASSIGN_STATUS: "PENDING",
      },
      { autoCommit: true }
    );

    console.log(
      "Request inserted successfully. Rows affected:",
      result.rowsAffected
    );
    return requestId;
  } catch (err) {
    console.error("Error inserting request:", err);
    throw err;
  } finally {
    // Close the connection
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing connection:", err);
      }
    }
  }
}

async function insertAccessRecords(requestId, userId, confidentialAccess) {
  let connection;
  const accessIds = []; // Array to hold generated ACCESS_IDs

  try {
    // Get a connection to the database
    connection = await oracledb.getConnection(dbConfig);

    // SQL query to insert an access record
    const insertSql = `
        INSERT INTO IFP_USER_ACCESS (REQUEST_ID, ACCESS_ID, USER_ID, DOMAIN, ACCESS_LEVEL, ACCESS_OPERATION)
        VALUES (:REQUEST_ID, :ACCESS_ID, :USER_ID, :DOMAIN, :ACCESS_LEVEL, :ACCESS_OPERATION)
      `;

    // Iterate over confidentialAccess and insert each record
    for (const access of confidentialAccess) {
      const accessId = uuid.v4(); // Generate a unique ACCESS_ID for each record
      const domain = access.domain.toUpperCase();
      const accessLevel = access.classification.toUpperCase();
      const accessOperation = "GRANT";

      // Execute the SQL query with the access data
      await connection.execute(
        insertSql,
        {
          REQUEST_ID: requestId,
          ACCESS_ID: accessId,
          USER_ID: userId,
          DOMAIN: domain,
          ACCESS_LEVEL: accessLevel,
          ACCESS_OPERATION: accessOperation,
        },
        { autoCommit: true }
      );

      console.log(`Access record inserted: ${accessId}`);
      accessIds.push({ accessId, domain, accessLevel }); // Add to the list
    }

    console.log("All access records inserted successfully.");
    return accessIds; // Return the generated ACCESS_IDs
  } catch (err) {
    console.error("Error inserting access records:", err);
    throw err;
  } finally {
    // Close the connection
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing connection:", err);
      }
    }
  }
}

async function insertApprovalRecords(accessList, status, approvalLevel = null) {
  let connection;

  try {
    // Get a connection to the database
    connection = await oracledb.getConnection(dbConfig);

    // SQL query to insert approval records
    const insertSql = `
        INSERT INTO IFP_USER_ACCESS_APPROVALS (ID, USER_ACCESS_ID, STATUS, APPROVAL_LEVEL, APPROVER_ID, REASON, ENTRA_ID_ASSIGN_STATUS)
        VALUES (:ID, :USER_ACCESS_ID, :STATUS, :APPROVAL_LEVEL, :APPROVER_ID, :REASON, :ENTRA_ID_ASSIGN_STATUS)
      `;

    // Iterate over accessList and insert each record
    for (const access of accessList) {
      const approvalId = uuid.v4(); // Generate a unique ID for each approval record
      const createdAt = new Date().toISOString(); // Generate current timestamp
      const updatedAt = createdAt;

      // Execute the SQL query with the approval data
      await connection.execute(
        insertSql,
        {
          ID: approvalId,
          USER_ACCESS_ID: access.accessId, // Use the ACCESS_ID as USER_ACCESS_ID
          STATUS: status,
          APPROVAL_LEVEL: approvalLevel,
          APPROVER_ID: null,
          REASON: null,
          ENTRA_ID_ASSIGN_STATUS: "PENDING", // Assuming ENTRA_ID_ASSIGN_STATUS is always PENDIN
        },
        { autoCommit: true }
      );

      console.log(`Approval record inserted: ${approvalId}`);
    }

    console.log("All approval records inserted successfully.");
  } catch (err) {
    console.error("Error inserting approval records:", err);
    throw err;
  } finally {
    // Close the connection
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing connection:", err);
      }
    }
  }
}

/*
---------------- SCRIPT STARTS HERE -----------------
*/

// Mock email IDs
let emails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

// Main Logic
emails.forEach(async (email) => {
  // All domains with
  const confidentialAccess = [
    { domain: "Agriculture & Environment", classification: "CONFIDENTIAL" },
    { domain: "Agriculture & Environment", classification: "OPEN" },
    { domain: "Economy", classification: "CONFIDENTIAL" },
    { domain: "Economy", classification: "OPEN" },
    { domain: "Industry & Business", classification: "CONFIDENTIAL" },
    { domain: "Industry & Business", classification: "OPEN" },
    { domain: "Population & Demographic", classification: "CONFIDENTIAL" },
    { domain: "Population & Demographic", classification: "OPEN" },
    { domain: "Social", classification: "CONFIDENTIAL" },
    { domain: "Social", classification: "OPEN" },
    { domain: "Census", classification: "CONFIDENTIAL" },
    { domain: "Census", classification: "OPEN" },
    { domain: "Labour Force", classification: "CONFIDENTIAL" },
    { domain: "Labour Force", classification: "OPEN" },
  ];

  const sensitiveAccess = [
    { domain: "Agriculture & Environment", classification: "SENSITIVE" },
    { domain: "Economy", classification: "SENSITIVE" },
    { domain: "Industry & Business", classification: "SENSITIVE" },
    { domain: "Population & Demographic", classification: "SENSITIVE" },
    { domain: "Social", classification: "SENSITIVE" },
    { domain: "Census", classification: "SENSITIVE" },
    { domain: "Labour Force", classification: "SENSITIVE" },
  ];

  const userId = uuid.v4();
  const user = {
    ID: userId,
    NAME: email.split("@")[0],
    EMAIL: encryptEmail(email),
    PHONE_NUMBER: "NA",
    ROLE: "USER",
    ENTITY_ID: "E49",
    STATUS: "REGISTERED",
    ACTIVATION_FLAG: "ACTIVE",
    DESIGNATION: "Developer",
  };
  await insertUser(user);
  console.log(`User ${email} with id ${userId} has been created`);
  const confRequestData = {
    USER_ID: userId,
    STATUS: "COMPLETED", // Replace with actual USER_ID
  };
  const confRequestId = await insertRequest(confRequestData);
  const accessIdsConfidential = await insertAccessRecords(
    confRequestId,
    userId,
    confidentialAccess
  );
  const randomSelection = selectRandomSensitiveAccess(sensitiveAccess);
  const sensRequestData = {
    USER_ID: userId,
    STATUS: "PENDING", // Replace with actual USER_ID
  };
  const sensRequestId = await insertRequest(sensRequestData);
  const accessIdsSensitive = await insertAccessRecords(
    sensRequestId,
    userId,
    randomSelection
  );

  await insertApprovalRecords(accessIdsSensitive, "PENDING", "DG");
  await insertApprovalRecords(accessIdsConfidential, "APPROVED");

  console.log(randomSelection);
});
