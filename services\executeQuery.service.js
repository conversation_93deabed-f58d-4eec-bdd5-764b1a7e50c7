const db = require('./database.service');
const { getADUserUpdateQuery, truncateADUser<PERSON>ist<PERSON>uery, getUserDataQuery, setUserActivationStatusQuery, getDisseminationAccessPolicyQuery, getUserAccessRequestsQuery, getUserAccessLevelsQuery, updateUserAccessRequestEntraIdStatusQuery, createUserAccessRequestQuery, createUserAccessQuery, createUserAccessApprovalQuery, updateUserAccessRequestStatusQuery, getFinalAccessQuery, getExistingUsersQuery, createUserQuery, updateUserDataQuery, incrementMobileSyncFailureFlagQuery, getPendingMobileSyncLinkedUsersQuery } = require('./getQuery.service');
const logger = require('../services/logger');

async function updateADUserList(usersData){
  let {query,binds} = await getADUserUpdateQuery(usersData)
  let data = await db.simpleExecute(query,binds)
  return data
}

async function truncateADUserList(usersData){
    let query = await truncateADUserListQuery(usersData)
    let data = await db.simpleExecute(query)
    return data
  }

async function getUserData(filters) {
  try {
    let { query, binds } = await getUserDataQuery(filters);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    console.error(err);
    throw err;
  }
}

async function setUserActivationStatus(userId, status) {
  try {
    let { query, binds } = await setUserActivationStatusQuery(userId, status);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    console.error(err);
    throw err;
  }
}

async function getDisseminationAccessPolicy(entityId) {
  try {
    let { query, binds } = await getDisseminationAccessPolicyQuery(entityId);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    logger.error(
      `Error fetching dissemination access policy: ${err.message}`, 
      { entityId }
    );
    throw err;
  }
}

async function getUserAccessRequests(filters) {
  try {
    logger.info('Fetching user access requests.', { filters });

    const { query, binds } = await getUserAccessRequestsQuery(filters);

    const data = await db.simpleExecute(query, binds);
    logger.info('User access requests retrieved successfully.', { resultCount: data.length });

    return data;
  } catch (err) {
    logger.error(
      `Error fetching user access requests: ${err.message}`,
      { filters }
    );
    throw err;
  }
}

async function getUserAccessLevels(filters) {
  try {
    logger.info('Fetching user access levels.', { filters });

    const { query, binds } = await getUserAccessLevelsQuery(filters);

    const data = await db.simpleExecute(query, binds);
    logger.info('User access levels retrieved successfully.', { resultCount: data.length });

    return data;
  } catch (err) {
    logger.error(
      `Error fetching user access levels: ${err.message}`,
      { filters }
    );
    throw err;
  }
}

async function updateUserAccessRequestEntraIdStatus(requestId, status) {
  try {
    logger.info('Updating user access request Entra ID status.', { requestId, status });

    const { query, binds } = await updateUserAccessRequestEntraIdStatusQuery(requestId, status);
    logger.debug('Constructed query for updating Entra ID status.', { query, binds });

    const data = await db.simpleExecute(query, binds);
    logger.info('User access request Entra ID status updated successfully.', { requestId, status });

    return data;
  } catch (err) {
    logger.error(
      `Error updating user access request Entra ID status: ${err.message}`,
      { requestId, status }
    );
    throw err;
  }
}

async function updateUserAccessRequestStatus(requestId, status) {
  try {
    logger.info('Updating user access request status.', { requestId, status });

    const { query, binds } = await updateUserAccessRequestStatusQuery(requestId, status);
    logger.debug('Constructed query for updating status.', { query, binds });

    const data = await db.simpleExecute(query, binds);
    logger.info('User access request status updated successfully.', { requestId, status });

    return data;
  } catch (err) {
    logger.error(
      `Error updating user access request status: ${err.message}`,
      { requestId, status }
    );
    throw err;
  }
}
async function createUserAccessRequest(requestId, userId) {
  try {
    let { query, binds } = await createUserAccessRequestQuery(
      requestId,
      userId
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    logger.error(
      `Error executing query createUserAccessRequest ${err}`
    );
    throw err;
  }
}

async function createUserAccess(
  requestId,
  accessId,
  userId,
  domain,
  accessLevel,
  accessOperation='GRANT'
) {
  try {
    let { query, binds } = await createUserAccessQuery(
      requestId,
      accessId,
      userId,
      domain,
      accessLevel,
      accessOperation
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    logger.error(
      `Error executing the query for createUserAccess${err}`
    );
    throw err;
  }
}

async function createUserAccessApproval(
  id,
  userAccessId,
  status,
  nextApprovalLevel,
  approverId = null,
  reason = null
) {
  try {
    let { query, binds } = await createUserAccessApprovalQuery(
      id,
      userAccessId,
      status,
      nextApprovalLevel,
      approverId,
      reason
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    logger.error(
      `Error executing query for createUserAccessApproval ${err}`
    );
    throw err;
  }
}

async function getFinalApproveUserAccess(accessIds) {
  try {
    let { query, binds } = await getFinalAccessQuery(accessIds);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    logger.error(`Error executing query for getFinalApproveUserAccess ${err}`);
    throw err;
  }
}

/** Get SCAD Existing users */
async function getExistingUsers() {
  try {
    let { query } = getExistingUsersQuery();
    let data = await db.simpleExecute(query);

    return data;
  } catch (err) {
    logger.error(`Error executing query for getExistingUsers ${err}`);
    throw err;
  }
}

async function createUserData(
  userId,
  name,
  email,
  phoneNumber,
  role,
  entityId,
  designation,
  existingUserLinkStatus,
  activationFlag = 'PENDING',
  ndaStatus = 1
) {
  try {
    let { query, binds } = await createUserQuery(
      userId,
      name,
      email,
      phoneNumber,
      role,
      entityId,
      designation,
      existingUserLinkStatus,
      activationFlag,
      ndaStatus
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `Error executing createUserData with error ${err}`
    );
    throw err;
  }
}

/**
 * Update user
 * @param {string} updateByField Field which record to be updated.
 * Either ID, EMAIL or MOBILE. Default is ID
 * @param {string} identifier Value for the updateByField
 * @param {object} updateFields Object of key value pairs where key
 * is the column name and value the value to be updated
 * @returns 
 */
async function updateUserData(updateByField = 'ID', identifier, updateFields) {
  try {
    let { query, binds } = updateUserDataQuery(
      updateByField,
      identifier,
      updateFields
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error updateUserData with error ${err}`);
    throw err;
  }
}

/**
 * Update user mobile sync failure
 * @param {string} userId 
 * @returns 
 */
async function incrementMobileSyncFailureFlag(userId) {
  try {
    let { query, binds } = incrementMobileSyncFailureFlagQuery(userId);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error updateUserData with error ${err}`);
    throw err;
  }
}

async function getLinkedPendingMobileSyncUsers() {
  try {
    const { query, binds } = getPendingMobileSyncLinkedUsersQuery();
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch(err) {
    log.error(`Error getLinkedPendingMobileSyncUsers with error ${err}`);
    throw err;
  }
}

module.exports = { 
  updateADUserList,
  truncateADUserList,
  getUserData,
  setUserActivationStatus,
  getDisseminationAccessPolicy,
  getUserAccessRequests,
  getUserAccessLevels,
  updateUserAccessRequestEntraIdStatus,
  createUserAccessRequest,
  createUserAccess,
  createUserAccessApproval,
  updateUserAccessRequestStatus,
  getExistingUsers,
  createUserData,
  getFinalApproveUserAccess,
  updateUserData,
  incrementMobileSyncFailureFlag,
  getLinkedPendingMobileSyncUsers,
}