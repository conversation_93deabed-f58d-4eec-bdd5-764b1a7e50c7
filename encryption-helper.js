require('dotenv').config();
const crypto = require('crypto');

const algorithm = 'aes-256-cbc'; // AES encryption algorithm
const ivLength = 16; // Initialization vector length
const iv = Buffer.alloc(16, 0);

function getKey() {
    // Loads the encryption key from the environment variable
    // const key = process.env.AES_SECRET_KEY;
    const key = 'ed9f5d9c975e9aa9d14bf50fcf08427e';
    if (!key || key.length !== 32) {
        throw new Error('AES_SECRET_KEY must be set and be 32 characters long in the environment variables.');
    }
    return Buffer.from(key, 'utf8');
}

function encryptEmail(email) {
    const key = getKey();
    const cipher = crypto.createCipheriv(algorithm, key, iv); // Use the fixed IV
    let encrypted = cipher.update(email, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    console.log(encrypted)
    return encrypted;
}

function decryptEmail(encryptedEmail) {
    const key = getKey();
    const decipher = crypto.createDecipheriv(algorithm, key, iv); // Use the fixed IV
    let decrypted = decipher.update(encryptedEmail, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    console.log(decrypted)
    return decrypted;
}

function encryptPhone(phone) {
    const key = getKey();
    const cipher = crypto.createCipheriv(algorithm, key, iv); // Use the fixed IV
    let encrypted = cipher.update(phone, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptPhone(encryptedPhone) {
    console.log(encryptedPhone)
    console.log(process.env.AES_SECRET_KEY)
    const key = getKey();
    const decipher = crypto.createDecipheriv(algorithm, key, iv); // Use the fixed IV
    let decrypted = decipher.update(encryptedPhone, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

// Export functions for use in other scripts
if (require.main === module) {
    // If run directly, show usage
    console.log('This module exports encryption functions. Import it in another script to use.');
} else {
    // Export for use as module
    module.exports = {
        encryptEmail,
        decryptEmail,
        encryptPhone,
        decryptPhone
    };
}

encryptEmail('<EMAIL>');
// decryptEmail('f6c130c1967628dd32baf418f00f4dc6')

//now to run this function: from the commad line -> node encryption-helper.js