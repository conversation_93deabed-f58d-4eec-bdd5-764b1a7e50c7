require('dotenv').config();

const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
const { decryptEmail } = require('../services/encryption.service');
const { getUserAccessRequests, getUserData, getUserAccessLevels, updateUserAccessRequestEntraIdStatus, getFinalApproveUserAccess, updateUserData } = require("../services/executeQuery.service");
const { inviteStatusEntraIdUser, assignUserToGroup, revokeUserFromGroup, checkUserInActiveDirectory } = require("../services/graph");
const { getBayaanExternalGroupMatrix, getDomainMatrixMap } = require('../services/group.service');
const logger = require("../services/logger");
const { syncUser, sendWelcomeEmail } = require('../services/mobile');

async function initDatabase() {
    logger.debug('Entering initDatabase function.');
    logger.info('Initializing database module.');
    try {
        await pgSequelize.authenticate();
        logger.info('Database initialized successfully.');
        logger.debug('Exiting initDatabase function successfully.');
    } catch (err) {
        logger.error(`Error during database initialization: ${err.message}`);
        throw err; // Re-throw the error after logging it for proper error handling upstream
    }
}


/**
 * Polls Entra ID invite acceptance status and assigns user to appropriate groups.
 */
async function userEntraIdGroupAssign() {
    try {
        logger.info('[ENTRAIDGROUPASSIGN CRON] Polling Invite Status start');
        const groupMatrix = getBayaanExternalGroupMatrix();
        const domainMatrixMap = await getDomainMatrixMap();

        const entraIdAcceptedRequests = await getUserAccessRequests({
            STATUS: 'COMPLETED',
            ENTRA_ID_ASSIGN_STATUS: 'PENDING'
        });

        logger.info('Fetched requests with completed status and pending group assignments.', {
            requestCount: entraIdAcceptedRequests.length
        });

        const requestPromises = [];

        for (const request of entraIdAcceptedRequests) {
            try{
                let userData = await getUserData({
                    ID: request.USER_ID,
                    ACTIVATION_FLAG: 'ACTIVE'
                });

                if (!userData.length) {
                    logger.warn(`No active user data found for user ID ${request.USER_ID}`);
                    throw new Error(`No active user data found for user ID ${request.USER_ID}`);
                }
                userData = userData[0];
                logger.debug(`Fetched user data for user ID ${request.USER_ID}`, { userData });
                const decryptedEmail = decryptEmail(userData.EMAIL);
                let proceedAssign = false
                let entraUserId = null
                if(+request.EXISTING_USER==1 || decryptedEmail.includes('@scad.gov.ae')){
                    const entraIdUserData = await checkUserInActiveDirectory(decryptedEmail.toLowerCase())
                    if (entraIdUserData != null){
                        entraUserId = entraIdUserData.id
                        proceedAssign = true
                    }
                }
                else{
                    const entraIdStatus = await inviteStatusEntraIdUser(decryptedEmail);
                    logger.debug(`Invite status for user ${decryptedEmail}: ${JSON.stringify(entraIdStatus)}`);
                
                    if (entraIdStatus.inviteStatus && entraIdStatus.accountEnabled) {
                        entraUserId = entraIdStatus.id
                        proceedAssign = true
                    }
                    logger.info(`[ENTRAIDGROUPASSIGN CRON] User ${decryptedEmail} is part of SCAD Entra ID`);
                }
                if (proceedAssign){
                    const userAccessLevels = await getFinalApproveUserAccess(
                      request.REQUEST_ID
                    );
                    logger.debug(`Fetched user access levels for request ID ${request.REQUEST_ID}`, { userAccessLevels });

                    const grantLevels = userAccessLevels
                        .filter(l => l.ACCESS_OPERATION === 'GRANT')
                        .map(l => ({
                            domain: l.DOMAIN,
                            classification: l.ACCESS_LEVEL
                        }));

                    const revokeLevels = userAccessLevels
                        .filter(l => l.ACCESS_OPERATION === 'REVOKE')
                        .map(l => ({
                            domain: l.DOMAIN,
                            classification: l.ACCESS_LEVEL
                        }));

                    const classificationLevels = ["open", "confidential", "sensitive", "secret"];
                    const classificationRank = {
                        open: 1,
                        confidential: 2,
                        sensitive: 3,
                        secret: 4,
                    };

                    const expandClassifications = (arr) => {
                        const result = [];
                        const domainMap = {};

                        arr.forEach(({ domain, classification }) => {
                            const currentRank = classificationRank[classification.toLowerCase()];
                            if (!domainMap[domain] || classificationRank[domainMap[domain].toLowerCase()] < currentRank) {
                                domainMap[domain] = classification;
                            }
                        });

                        for (const [domain, highestClassification] of Object.entries(domainMap)) {
                            const highestRank = classificationRank[highestClassification.toLowerCase()];
                            classificationLevels.forEach((classification) => {
                                if (classificationRank[classification] <= highestRank) {
                                    result.push({ domain, classification });
                                }
                            });
                        }

                        return result;
                    };

                    const expandedGrantLevels = expandClassifications(grantLevels);
                    logger.debug(`Expanded classification levels for user ${decryptedEmail}`, { expandedGrantLevels });

                    const expandClassificationsRevoke = (arr) => {
                        const result = [];
                        const domainMap = {};
                    
                        arr.forEach(({ domain, classification }) => {
                            const currentRank = classificationRank[classification.toLowerCase()];
                            if (!domainMap[domain] || classificationRank[domainMap[domain].toLowerCase()] > currentRank) {
                                domainMap[domain] = classification;
                            }
                        });
                    
                        for (const [domain, highestClassification] of Object.entries(domainMap)) {
                            const highestRank = classificationRank[highestClassification.toLowerCase()];
                            classificationLevels.forEach((classification) => {
                                if (classificationRank[classification] >= highestRank) { // This condition returns higher levels
                                    result.push({ domain, classification });
                                }
                            });
                        }
                    
                        return result;
                    };

                    const expandedRevokeLevels = expandClassificationsRevoke(revokeLevels);
                    logger.debug(`Expanded classification levels for user ${decryptedEmail}`, { expandedRevokeLevels });

                    // Assign groups for Users based on requests
                    const matrix_groups_assign = expandedGrantLevels.map(level =>
                        `MATRIX__${domainMatrixMap[level.domain.toUpperCase()]}__${level.classification.toUpperCase()}`
                    );

                    logger.info(`Groups to be assigned to ${decryptedEmail}: [${matrix_groups_assign.join(', ')}]`);

                    const matrixGroupIdsAssign = matrix_groups_assign.map(g => {
                        const groups = Object.entries(groupMatrix)
                            .filter(([id, name]) => g === name)
                            .map(([id, name]) => id);

                        if (groups.length) {
                            return groups[groups.length - 1];
                        } else {
                            logger.warn(`No matching group found for ${g}`);
                        }
                    }).filter(g => g);

                    if (matrixGroupIdsAssign.length) {
                        let platformAccessGroups = []
                        if (process.env.NODE_ENV === 'prod') {
                            if (decryptedEmail.includes('@scad.gov.ae')) {
                                platformAccessGroups = ['ad85f6c4-04af-48ad-aed6-c63e7f9f768d'] //Prod Internal
                            }
                            else{                          
                                platformAccessGroups = ['a0d83e83-22c0-4406-b0b8-6eb7246aa436'] //Prod External
                            }
                        }
                        else{
                            platformAccessGroups = ['93732bd5-d769-4b9a-bb0e-2e414a50b463'] //Staging Internal
                        }

                        let addedExternalGroups = matrixGroupIdsAssign.concat(platformAccessGroups)
                        await assignUserToGroup(entraUserId, addedExternalGroups);
                        await updateUserAccessRequestEntraIdStatus(request.REQUEST_ID, 'COMPLETED');
                        logger.info(`Groups assigned to ${decryptedEmail}: [${matrix_groups_assign.join(', ')}]`);
                    }

                    // Revoke groups for Users based on requests
                    const matrix_groups_revoke = expandedRevokeLevels.map(level =>
                        `MATRIX__${domainMatrixMap[level.domain.toUpperCase()]}__${level.classification.toUpperCase()}`
                    );

                    logger.info(`Groups to be revoked from ${decryptedEmail}: [${matrix_groups_revoke.join(', ')}]`);

                    const matrixGroupIdsRevoke = matrix_groups_revoke.map(g => {
                        const groups = Object.entries(groupMatrix)
                            .filter(([id, name]) => g === name)
                            .map(([id, name]) => id);

                        if (groups.length) {
                            return groups[groups.length - 1];
                        } else {
                            logger.warn(`No matching group found for ${g}`);
                            throw new Error(`Group mapping failed for ${g}`);
                        }
                    });

                    if (matrixGroupIdsRevoke.length) {
                        await revokeUserFromGroup(entraUserId, matrixGroupIdsRevoke);
                        await updateUserAccessRequestEntraIdStatus(request.REQUEST_ID, 'COMPLETED');
                        logger.info(`Groups removed from ${decryptedEmail}: [${matrix_groups_revoke.join(', ')}]`);
                    }

                    // Sync user with mobile and trigger welcome Email
                    if (
                      userData.MOBILE_SYNC_STATUS == "PENDING" &&
                      userData.MOBILE_SYNC_FAILED_ATTEMPTS < 5
                    ) {
                      await syncUser(userData);
                    } else if (userData.MOBILE_SYNC_STATUS == "NA_ONLY_WELCOME_EMAIL") {
                      const emailData = {
                        name: userData.NAME,
                        email: decryptEmail(userData.EMAIL),
                        role: userData.ROLE,
                      };
                      const sent = await sendWelcomeEmail(emailData, {});
                      if (sent) {
                        await updateUserData("EMAIL", request.EMAIL, {
                          MOBILE_SYNC_STATUS: "NA_WITH_EMAIL_SENT",
                        });
                      }
                    }

                    // Revoke access to be implemented
                }
            }
            catch (err) {
                logger.error(`Error during ENTRA ID group assignment for user ${request.USER_ID}: ${err.message}`);
            }
        }

        await Promise.all(requestPromises);
        logger.info('[ENTRAIDGROUPASSIGN CRON] Polling Invite Status completed');
    } catch (err) {
        logger.error(`Error during ENTRA ID group assignment poll: ${err.message}`);
        throw err;
    }
}

const run = async () => {
    await initDatabase();
  
    try {
        await userEntraIdGroupAssign();
        logger.info("Entra ID Acceptance completed.");
    } catch (err) {
        logger.error(`Failed to check Entra ID Acceptance: ${err.message}`);
    }
};

module.exports = {
    run
};