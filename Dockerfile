# ---- Base Node ----
FROM registry.access.redhat.com/ubi8/nodejs-16:latest AS base

USER root

FROM base AS release
USER root
RUN yum install -y --nogpgcheck --disableplugin=subscription-manager libaio wget
WORKDIR /opt/oracle

RUN wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
    unzip instantclient-basiclite-linuxx64.zip && rm -f instantclient-basiclite-linuxx64.zip && \
    cd /opt/oracle/instantclient* && rm -f *jdbc* *occi* *mysql* *mql1* *ipc1* *jar uidrvci genezi adrci && \
    echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && ldconfig

WORKDIR /home/<USER>/app
COPY . .
RUN tar -xzvf node_modules.tar.gz
RUN rm -rf node_modules.tar.gz
RUN chown -R 1001:root /home/<USER>/app && chmod -R og+rwx /home/<USER>/app
ENV NO_UPDATE_NOTIFIER true
RUN echo "#!/bin/sh \n\
echo "fs.inotify.max_user_watches before update" \n\
cat /etc/sysctl.conf\n\
echo "______________________________________________updating inotify ____________________________________" \n\
echo fs.inotify.max_user_watches=524288 | tee -a /etc/sysctl.conf && sysctl -p \n\
echo "updated value is" \n\
cat /etc/sysctl.conf | grep fs.inotify \n\
npm config set strict-ssl=false  \n\
exec npm start"

USER 1001
# EXPOSE TARGET PORT
EXPOSE 3000
CMD npm config set strict-ssl=false
CMD npm start