const axios = require("axios").default;
var QRCode = require("qrcode");

const { decryptEmail, decryptPhone } = require("./encryption.service");
const logger = require("../services/logger");
const { sendEmail } = require("./email/email.service");
const {
  updateUserData,
  incrementMobileSyncFailureFlag,
} = require("./executeQuery.service");
require("dotenv").config();

async function syncUser(user) {
  logger.debug(
    `[SYNCUSER FUNCTION] Entering syncUser function for user: ${JSON.stringify(
      user
    )}`
  );
  try {
    const decryptedEmail = decryptEmail(user.EMAIL);
    const mobileBasePath = process.env.MOBILE_BASEPATH;
    const syncApiEndpoint = mobileBasePath + "/api/v1/users/sync/";
    const headers = {
      "Content-Type": "application/json",
      "x-api-key": process.env.MOBILE_API_KEY,
    };

    await axios
      .post(
        syncApiEndpoint,
        {
          name: user.NAME,
          email: decryptedEmail,
          role: "normal-user",
          uae_pass_uuid: user.ID,
          user_type: "g",
          entity: user.ENTITY_ID,
          designation: user.DESIGNATION,
          phone:
            user.PHONE_NUMBER == "NA" ? null : decryptPhone(user.PHONE_NUMBER),
          primary_super_user: user.ROLE == "PRIMARY_SUPERUSER" ? true : false,
          dg_user: user.ROLE == "DG" ? true : false,
        },
        {
          headers: headers,
        }
      )
      .then(async (response) => {
        if (response.status == 201 || response.status == 200) {
          logger.info(
            `[SYNCUSER FUNCTION] User sync request for user: ${decryptedEmail} completed successfully}`
          );
          await updateUserData("ID", user.ID, { MOBILE_SYNC_STATUS: "SYNCED" });
          const { android_mft_url, android_mft_url_ar, android_mft_password } =
            response.data;
          const emailData = {
            name: user.NAME,
            email: decryptedEmail,
            role: user.ROLE,
          };
          const mftDetails = {
            android_mft_url,
            android_mft_url_ar,
            android_mft_password,
          };
          await sendWelcomeEmail(emailData, mftDetails);
        } else {
          logger.error(
            `[SYNCUSER FUNCTION] Unrecognized response code for sync request: ${response.status} with data: ${response.data}`
          );
        }
      })
      .catch(async (error) => {
        logger.error(
          `[SYNCUSER FUNCTION] Error during Mobile API Sync for user: ${decryptedEmail}: ${JSON.stringify(error.response.data)}`
        );
        await incrementMobileSyncFailureFlag(user.ID);
      });
    logger.debug(
      `[SYNCUSER FUNCTION] syncUser function for user: ${user.EMAIL} successfully.`
    );
  } catch (err) {
    logger.error(
      `[SYNCUSER FUNCTION] Error during user sync for user: ${user.EMAIL}. Error: ${err.message}`
    );
    throw err;
  }
}

/**
 * Sends Bayaan platform welcome email to the user.
 *
 * Contains standard platform login url along with links and
 * QR code to apple store and user specific android download
 * url and QR code.
 * 
 * Returns 'sent status' as bool
 * @param {*} userInfo
 * @param {*} mftDetails
 */
async function sendWelcomeEmail(userInfo, mftDetails) {
  let emailSent;
  try {
    const emailData = {
      recipientName: userInfo.name,
      recepientEmail: userInfo.email,
      recipientRole: ["PRIMARY_SUPERUSER", "SECONDARY_SUPERUSER"].includes(
        userInfo.role
      )
        ? "SUPERUSER"
        : userInfo.role,
      emailType: "PLATFORM_WELCOME_EMAIL",
      subject: "Bayaan - Welcome to Bayaan",
      bayaanWebUrl: process.env.PLATFORM_BASEPATH + "/login",
      appleStoreUrl: "https://apps.apple.com/app/bayaan-gov/id6475820691",
      currentYear: new Date().getFullYear(),
      isDgUser: userInfo.role == "DG" ? true : false,
      isPrimarySuperuser: ["PRIMARY_SUPERUSER", "SECONDARY_SUPERUSER"].includes(
        userInfo.role
      )
        ? true
        : false,
    };

    if (typeof mftDetails == 'object' && Object.keys(mftDetails).length) {
      emailData.isMobileSynced = true;
      emailData.androidMFTUrlAr = mftDetails.android_mft_url_ar;
      emailData.androidMFTQrCodeAr = await QRCode.toDataURL(
        mftDetails.android_mft_url_ar
      );
      emailData.androidMFTPassword = mftDetails.android_mft_password;
      emailData.androidMFTUrl = mftDetails.android_mft_url;
      emailData.androidMFTUrlQrCode = await QRCode.toDataURL(
        mftDetails.android_mft_url
      );
    }

    await sendEmail(emailData);
    emailSent = true;
  } catch (error) {
    emailSent = false;
    logger.error(
      `[SENDWELCOMEEMAIL FUNC] Something went wrong during welcome email for: ${userInfo.email}: ${error}`
    );
  }
  return emailSent;
}

module.exports = {
  syncUser,
  sendWelcomeEmail,
};
