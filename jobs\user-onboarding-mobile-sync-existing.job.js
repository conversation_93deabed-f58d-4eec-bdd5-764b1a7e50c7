/**
 * Mobile sync for existing users.
 *
 * Ensures that linked existing users are synced with Mobile backend
 * to allow them to login and access <PERSON><PERSON>an <PERSON>pp.
 */
const database = require("../services/database.service");
const { getLinkedPendingMobileSyncUsers } = require("../services/executeQuery.service");
const logger = require("../services/logger");
const { syncUser, sendWelcomeEmail } = require("../services/mobile");

require("dotenv").config();

async function initDatabase() {
  logger.debug("Entering initDatabase function.");
  logger.info("Initializing database module.");
  try {
    await database.initialize();
    logger.info("Database initialized successfully.");
    logger.debug("Exiting initDatabase function successfully.");
  } catch (err) {
    logger.error(`Error during database initialization: ${err.message}`);
    throw err; // Re-throw the error after logging it for proper error handling upstream
  }
}

async function existingUserMobileSync() {
  try {
    logger.info("[EXISTINGUSERMOBILESYNC CRON] Polling Invite Status start");

    const linkedExistingUsers = await getLinkedPendingMobileSyncUsers();
    logger.info(
      "[EXISTINGUSERMOBILESYNC CRON] Fetched existing users who have completed linking: $"
    );

    for (const user of linkedExistingUsers) {
      // Sync user with mobile and trigger welcome Email
      if (
        user.MOBILE_SYNC_STATUS == "PENDING" &&
        user.MOBILE_SYNC_FAILED_ATTEMPTS < 5
      ) {
        await syncUser(user);
      } else if (user.MOBILE_SYNC_STATUS == "NA_ONLY_WELCOME_EMAIL") {
        const emailData = {
          name: user.NAME,
          email: decryptEmail(user.EMAIL),
          role: user.ROLE,
        };
        const sent = await sendWelcomeEmail(emailData, {});
        if (sent) {
          await updateUserData("EMAIL", request.EMAIL, {
            MOBILE_SYNC_STATUS: "NA_WITH_EMAIL_SENT",
          });
        }
      }
    }

    logger.info(
      "[EXISTINGUSERMOBILESYNC CRON] Exisiting modile sync completed"
    );
  } catch (err) {
    logger.error(
      `[EXISTINGUSERMOBILESYNC CRON] Error during mobile sync for existing user: ${err.message}`
    );
    throw err;
  }
}

const run = async () => {
  await initDatabase();

  try {
    await existingUserMobileSync();
    logger.info(
      "[EXISTINGUSERMOBILESYNC CRON] Exisiting modile sync completed"
    );
  } catch (err) {
    logger.error(
      `[EXISTINGUSERMOBILESYNC CRON] Error during mobile sync completed: ${err.message}`
    );
  }
};

module.exports = {
  run,
};
