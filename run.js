const { program } = require("commander");
require('dotenv').config();

program.requiredOption("--job-id <jobName>", "The ID of the Cron job to run");

program.parse();

const { jobId } = program.opts();

async function main(jobId) {
  let job;

  switch (jobId) {
    case "user-ad-sync":
      job = require("./jobs/user-ad-sync.job");
      break;
    case "kpi-logs-sync":
      job = require("./jobs/kpi-logs-sync.job");
      break;
    case "entra-id-invite":
      job = require("./jobs/entra-id-invite.job");
      break;
    case "entra-id-acceptance-poll":
      job = require("./jobs/entra-id-acceptance-poll.job");
      break;
    case "entra-id-group-assign":
      job = require("./jobs/entra-id-group-assign.job");
      break;
    case "mobile-sync-users":
      job = require("./jobs/mobile-sync-users.job");
      break;
    case "entra-id-helper":
      job = require("./jobs/entra-id-helper.job");
      break;
    case "user-onboarding-sync-existing":
      job = require("./jobs/user-onboarding-sync-existing.job");
      break;
    case "existing-user-mobile-sync":
      job = require("./jobs/user-onboarding-mobile-sync-existing.job");
      break;
    case "update-dissemination-policy":
      job = require("./scripts/update-dissemination-policy");
      break;
    case "ad-sync-users":
      job = require("./jobs/ad-sync-users.job");
      break;
    default:
      console.log("default job");
      process.exit(0);
  }
  
  try {
    await job.run();
  } catch (err) {
    console.error(err);
  }
  
  process.exit(0);    
}

main(jobId)
