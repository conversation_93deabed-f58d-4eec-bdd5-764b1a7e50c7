const axios = require("axios").default;
require("dotenv").config();
const constants = require("../config/constants.json");

const { getAuthToken } = require("../services/graph");

async function getUserIdByEmail(msalToken, emails) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
    },
  };

  let userIds = [];
  for (const email of emails) {
    try {
      const response = await axios.get(
        `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$filter=mail eq '${email}'`,
        options
      );

      if (response.data.value && response.data.value.length > 0) {
        // Assuming there's only one user with the given email
        const userId = response.data.value[0].id;
        userIds.push(userId);
        // return userId;
      } else {
        console.log(`User with email ${email} not found.`);
        // return null;
      }
    } catch (err) {
      console.error("Error fetching user ID:", err);
      // return null;
    }
  }
  return userIds;
}

async function getUserGroups(msalToken, userId) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
    },
  };

  try {
    const response = await axios.get(
      `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${userId}/memberOf`,
      options
    );

    // Extract group IDs from the response
    const groups = response.data.value
      .filter((item) => item["@odata.type"] === "#microsoft.graph.group") // Only include group objects
      .map((group) => ({ id: group.id, displayName: group.displayName }));

    return JSON.stringify(groups);
  } catch (err) {
    console.error(`Error fetching groups for user ${userId}:`, err);
    return [];
  }
}

async function findUsersByEmailDomain(msalToken, domain) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
      ConsistencyLevel: "eventual",
    },
    params: {
      $count: true, // Required when using advanced queries like endsWith
      $filter: `endswith(mail, '${domain}')`,
    },
  };

  try {
    const response = await axios.get(
      `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}`,
      options
    );

    const users = response.data.value;

    if (users.length > 0) {
      console.log(`Found ${users.length} users with domain ${domain}:`);
      users.forEach((user) => {
        console.log(`User ID: ${user.id}, Email: ${user.mail}`);
      });
    } else {
      console.log(`No users found with domain ${domain}.`);
    }

    return users;
  } catch (err) {
    console.error(`Error finding users with domain ${domain}:`, err);
    return [];
  }
}

async function getAllUsers(msalToken) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
    },
  };

  let users = [];
  let nextLink = `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}`;

  // Paginate through all users
  try {
    while (nextLink) {
      const response = await axios.get(nextLink, options);
      users = users.concat(response.data.value);

      // Check if there is a nextLink (indicating more results)
      nextLink = response.data["@odata.nextLink"] || null;
    }
  } catch (err) {
    console.error("Error retrieving users:", err);
  }

  return users;
}

function filterUsersByEmailDomain(users, domain) {
  return users.filter((user) => user.mail && user.mail.endsWith(domain));
}

async function unassignUserFromGroup(msalToken, groupId, userId) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
    },
  };

  try {
    await axios.delete(
      `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${groupId}/members/${userId}/$ref`,
      options
    );
    console.log(`Successfully unassigned user ${userId} from group ${groupId}`);
  } catch (err) {
    console.error(
      `Error unassigning user ${userId} from group ${groupId}:`,
      err.message
    );
  }
}

async function assignUserToGroup(msalToken, groupId, userId) {
  const options = {
    headers: {
      Authorization: `Bearer ${msalToken}`,
      "Content-Type": "application/json",
    },
  };

  const body = {
    "@odata.id": `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${userId}`,
  };

  try {
    await axios.post(
      `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${groupId}/members/$ref`,
      body,
      options
    );
    console.log(`Successfully added user ${userId} to group ${groupId}`);
  } catch (err) {
    console.error(
      `Error assigning user ${userId} to group ${groupId}:`,
      err.response?.data || err.message
    );
  }
}

function getUnallowedGroupIds(all = false) {
  const groupMatrix = require("../services/helpers/groupMatrix.json");
  let results = [];
  if (all) {
    return Object.keys(groupMatrix);
  }
  Object.entries(groupMatrix).map(([key, value]) => {
    if (value.includes("SENSITIVE") || value.includes("SECRET")) {
      results.push(key);
    }
  });
  return results;
}

async function run() {
  const authToken = await getAuthToken();
  /* -----------------GET USER ENTRA ID ID VIA EMAIL--------------------- */
  // const emails = [
  //   // "<EMAIL>",
  //   // "<EMAIL>",
  //   "<EMAIL>",
  //   // "<EMAIL>",
  //   // "<EMAIL>",
  //   // "<EMAIL>",
  //   // "<EMAIL>",
  //   // "<EMAIL>"
  // ]; // Replace with the actual user email
  // const userId = await getUserIdByEmail(authToken, emails);

  // if (userId) {
  //   console.log(`User ID for ${emails}: ${userId}`);
  // }

  /* -----------------GET USER GROUPS-----------------*/
  // const userIds = [
  //   // "1c03b634-ceef-41fc-aeb1-1a68ad04e5be",
  //   // "983aacbe-ea10-4598-913d-a6243d3f4206",
  //   "243c673e-39f6-43df-9666-dae26c88616b",
  //   // "024a54b2-5194-4f58-ba43-d425ad2611a6"
  // ]

  // for (const userIdx of userIds) {
  //   const groups = await getUserGroups(authToken, userIdx)
  //   console.log(`Groups for user: ${userIdx}: ${groups}\n`)
  // }

  /* ----------------------FIND USER BY EMAIL----------------------*/
  // const domain = "mailosaur.net"; // Replace with the domain you want to search for
  // const users = await findUsersByEmailDomain(authToken, domain);
  // const users = await getAllUsers(authToken)

  // if (users.length > 0) {
  //   console.log(`Total users found: ${users.length}`);
  // }

  // // Step 1: Get all users from Microsoft Graph
  // const allUsers = await getAllUsers(authToken);

  // // Step 2: Filter users by email domain
  // const filteredUsers = filterUsersByEmailDomain(allUsers, domain);

  // // Step 3: Log the results
  // if (filteredUsers.length > 0) {
  //   console.log(`Found ${filteredUsers.length} users with domain ${domain}:`);
  //   filteredUsers.forEach(user => {
  //     console.log(`User ID: ${user.id}, Email: ${user.mail}`);
  //   });
  // } else {
  //   console.log(`No users found with domain ${domain}.`);
  // }

  /* ----------------- UNASSIGN GROUPS --------------------*/
  // const groupIds = [
  //   "643fcd42-10a3-4d7d-a49b-797bbf67f54e",
  //   "7ab595c3-bc6c-4676-9e1b-7f1d145abd74",
  //   "464f21b4-0b9a-49c3-a55b-23d6416177fe",
  //   "f7b36748-26db-4d23-b7ce-4d013e4e3838",
  //   "6f437b36-da26-428e-bb93-4092934daac8",
  //   "0b9daf3d-e169-41b8-a4a9-8b29b5cbb77a",
  //   "36606e9a-0110-4c7b-b49f-f10c6db67a6a",
  //   "9dda8225-8007-448b-af8b-d40697843f73",
  //   "c25b3f6c-1b36-4aa0-aadc-c9535c38e79c",
  //   "9f150a7d-bbcd-4b75-9eea-a3a5305c4e66",
  //   "a456f6c6-8af0-4380-8df4-5cfd34966ec8",
  //   "1bfe86ce-bf85-401d-a650-2545d694771d"
  // ]

  // const sensitiveAndAboveGroups = [
  //   // '2e49b670-3e66-45cd-8517-54771843de3c',
  //   // 'f81af515-cbef-4790-89ea-5ecd60e0d5a5',
  //   '643fcd42-10a3-4d7d-a49b-797bbf67f54e',
  //   '7ab595c3-bc6c-4676-9e1b-7f1d145abd74',
  //   // '613eaca5-6511-4233-8aec-2636f9e1c4a9',
  //   // '084322e0-96ce-4795-9ee3-0d1b76cf6153',
  //   '464f21b4-0b9a-49c3-a55b-23d6416177fe',
  //   'f7b36748-26db-4d23-b7ce-4d013e4e3838',
  //   // '2018b370-194e-4901-bd2c-092c6c135bd9',
  //   // '6ef68eb3-f6bf-4d02-b94e-9887642b3606',
  //   '6f437b36-da26-428e-bb93-4092934daac8',
  //   '0b9daf3d-e169-41b8-a4a9-8b29b5cbb77a',
  //   // 'a17dd1ed-fe7f-4553-bc3a-c872b2d339b6',
  //   // 'f3b3553a-b41f-4998-9a33-b4a1465348aa',
  //   '36606e9a-0110-4c7b-b49f-f10c6db67a6a',
  //   '9dda8225-8007-448b-af8b-d40697843f73',
  //   // '00c65867-7dd8-48f3-8d92-de1e45d5acce',
  //   // 'c3a51461-9aaf-4ec7-8e88-066bac9d7909',
  //   'c25b3f6c-1b36-4aa0-aadc-c9535c38e79c',
  //   '9f150a7d-bbcd-4b75-9eea-a3a5305c4e66',
  //   // 'ba1f17a0-ccb5-454b-a0f3-6b498b15e414',
  //   // '52cc3eee-1e14-4a02-a452-0c07a4c6b4c4',
  //   'a456f6c6-8af0-4380-8df4-5cfd34966ec8',
  //   '1bfe86ce-bf85-401d-a650-2545d694771d',
  //   // '54698906-f122-4d75-9901-5d984c11a314'
  // ]

  // const allGroups = [
  //   '2e49b670-3e66-45cd-8517-54771843de3c',
  //   'f81af515-cbef-4790-89ea-5ecd60e0d5a5',
  //   '643fcd42-10a3-4d7d-a49b-797bbf67f54e',
  //   '7ab595c3-bc6c-4676-9e1b-7f1d145abd74',
  //   '613eaca5-6511-4233-8aec-2636f9e1c4a9',
  //   '084322e0-96ce-4795-9ee3-0d1b76cf6153',
  //   '464f21b4-0b9a-49c3-a55b-23d6416177fe',
  //   'f7b36748-26db-4d23-b7ce-4d013e4e3838',
  //   '2018b370-194e-4901-bd2c-092c6c135bd9',
  //   '6ef68eb3-f6bf-4d02-b94e-9887642b3606',
  //   '6f437b36-da26-428e-bb93-4092934daac8',
  //   '0b9daf3d-e169-41b8-a4a9-8b29b5cbb77a',
  //   'a17dd1ed-fe7f-4553-bc3a-c872b2d339b6',
  //   'f3b3553a-b41f-4998-9a33-b4a1465348aa',
  //   '36606e9a-0110-4c7b-b49f-f10c6db67a6a',
  //   '9dda8225-8007-448b-af8b-d40697843f73',
  //   '00c65867-7dd8-48f3-8d92-de1e45d5acce',
  //   'c3a51461-9aaf-4ec7-8e88-066bac9d7909',
  //   'c25b3f6c-1b36-4aa0-aadc-c9535c38e79c',
  //   '9f150a7d-bbcd-4b75-9eea-a3a5305c4e66',
  //   'ba1f17a0-ccb5-454b-a0f3-6b498b15e414',
  //   '52cc3eee-1e14-4a02-a452-0c07a4c6b4c4',
  //   'a456f6c6-8af0-4380-8df4-5cfd34966ec8',
  //   '1bfe86ce-bf85-401d-a650-2545d694771d',
  //   '54698906-f122-4d75-9901-5d984c11a314'
  // ]

  // for (const userId of userIds) {
  //   for (const groupId of allGroups) {
  //     await unassignUserFromGroup(authToken, groupId, userId);
  //   }
  // }

  /* ------------ASSIGN GROUPS------------------*/
  // const userId = "024a54b2-5194-4f58-ba43-d425ad2611a6"; // Replace with actual user ID
  // const groupId = "93732bd5-d769-4b9a-bb0e-2e414a50b463"; // Replace with actual group ID

  // await assignUserToGroup(authToken, groupId, userId);

  // console.log(getUnallowedGroupIds(true))
  process.exit(0);
}

module.exports = { run };
