'use strict';
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'dev';
const config = require(__dirname + '/../config/database.js')[env];
const db = {};

const platformConfig = config || {}

let sequelize;

if (platformConfig.use_env_variable) {
  sequelize = new Sequelize(process.env[platformConfig.use_env_variable], platformConfig);
} else {
  sequelize = new Sequelize(platformConfig.database, platformConfig.username, platformConfig.password, platformConfig);
}

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
