var ioRedis = require('ioredis');

function flushKeys(pattern) {
    
    var redis = new ioRedis({
        port: process.env.REDIS_PORT,
        host: process.env.REDIS_HOST,
    });

    return redis.keys(pattern).then(function (keys) {
        var pipeline = redis.pipeline();
        keys.forEach(function (key) {
            pipeline.del(key);
        });
        return pipeline.exec();
    }).finally(function () {
        redis.quit();
    });
}

const getRedisKeys = (pattern) => {
    const redis = new ioRedis({
        port: process.env.REDIS_PORT,
        host: process.env.REDIS_HOST,
    });
    return redis.keys(pattern).finally(() => redis.quit());
};

module.exports = {
  flushKeys,
  getRedisKeys,
};