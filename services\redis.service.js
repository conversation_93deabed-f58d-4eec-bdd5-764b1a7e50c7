var ioRedis = require('ioredis');

function getRedisConfig() {
    let redisConf = {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT
    };

    if (process.env.REDIS_PASSWORD && process.env.REDIS_PASSWORD !== '' && process.env.REDIS_PASSWORD !== null) {
        redisConf.password = process.env.REDIS_PASSWORD;
        redisConf.tls = {};
    }

    return redisConf;
}

function flushKeys(pattern) {
    
    var redis = new ioRedis(getRedisConfig());

    return redis.keys(pattern).then(function (keys) {
        var pipeline = redis.pipeline();
        keys.forEach(function (key) {
            pipeline.del(key);
        });
        return pipeline.exec();
    }).finally(function () {
        redis.quit();
    });
}

const getRedisKeys = (pattern) => {
    const redis = new ioRedis(getRedisConfig());
    return redis.keys(pattern).finally(() => redis.quit());
};

module.exports = {
  flushKeys,
  getRedisKeys,
  getRedisConfig,
};