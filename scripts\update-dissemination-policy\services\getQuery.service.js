function bulkAddPolicyRecordQuery(
  entityId,
  entityName,
  domain,
  classifications
) {
  let records = "";

  for (const classification of classifications) {
    records += `
      INTO IFP_DISS_ACCESS_POLICY (ENTITY_ID, NAME, "DOMAIN", CLASSIFICATION)
      VALUES ('${entityId}', '${entityName}', '${domain}', '${classification}')
    `;
  }

  const finalQuery = `
    INSERT ALL
    ${records}
    SELECT * FROM dual
  `;

  return finalQuery;
}

module.exports = {
  bulkAddPolicyRecordQuery,
};
