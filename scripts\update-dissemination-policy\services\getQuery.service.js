const logger = require("../../../services/logger");
//Done
async function bulkAddPolicyRecordQuery(
  entityId,
  entityName,
  domain,
  classifications
) {
  try {
    logger.info('Constructing bulkAddPolicyRecordQuery.', {
      entityId, entityName, domain, count: Array.isArray(classifications) ? classifications.length : 0
    });

    if (!Array.isArray(classifications) || classifications.length === 0) {
      throw new Error('classifications must be a non-empty array');
    }

    const binds = {
      entityId: entityId ?? null,
      entityName: entityName ?? null,
      domain: domain ?? null
    };

    const valueTuples = classifications.map((cls, i) => {
      const key = `classification${i}`;
      binds[key] = cls ?? null; // ensure undefined → null
      return `(:entityId, :entityName, :domain, :${key})`;
    });

    const query = `
      INSERT INTO "IFP_DISS_ACCESS_POLICY"
        ("ENTITY_ID","NAME","DOMAIN","CLASSIFICATION")
      VALUES
        ${valueTuples.join(', ')}
    `;

    logger.info('bulkAddPolicyRecordQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error constructing bulkAddPolicyRecordQuery: ${err.message}`, {
      entityId, entityName, domain, classifications
    });
    throw err;
  }
}


module.exports = {
  bulkAddPolicyRecordQuery,
};
