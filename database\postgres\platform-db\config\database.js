require('dotenv').config();

const nodeEnv = process.env.NODE_ENV || 'local';

function getSSLConfig(prefix) {
  if (process.env[`${prefix}_PG_SQL_SSL`] === 'true') {
    return { ssl: {require:true} };
  }
  return {};
}

function db(prefix,extraDialectOptions={}) {
  return {
    username: process.env[`${prefix}_PG_SQL_USER`],
    password: process.env[`${prefix}_PG_SQL_PASSWORD`],
    database: process.env[`${prefix}_PG_SQL_DATABASE`],
    host:     process.env[`${prefix}_PG_SQL_HOST`],
    dialect: 'postgres',
    dialectOptions: {
      ...getSSLConfig(prefix),
      ...extraDialectOptions
    }
  };
}

module.exports = {
  [nodeEnv]: {
    ...db('BAYAANPLATFORM')
  },
};
