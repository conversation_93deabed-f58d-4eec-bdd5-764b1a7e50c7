const db = require("../../services/database.service");
const logger = require("../../services/logger");

function addPolicyRecord(entityId, entityName, domain, classification) {
  const binds = { entityId, entityName, domain, classification }
  const query = `
    INSERT
      INTO
      IFP_DISS_ACCESS_POLICY (ENTITY_ID,
      NAME,
      "DOMAIN",
      CLASSIFICATION)
    VALUES (:entityId,
    :entityName,
    :domain,
    :classification)
  `;
}
