#!/usr/bin/env node
/**
 * Enhanced JavaScript script to fetch ALL users from Azure AD with their email aliases
 * Uses Microsoft Graph API with pagination support
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Environment variables configuration
const ENV_VARS = {
    USER_CLIENT_ID: '32dcfbb1-a089-4749-8c57-ce11485a617b',
    USER_CLIENT_SECRET: '****************************************',
    USER_TENANT_ID: '6926239f-3483-4451-8452-48ee3bee086f',
    USER_GRAPH_ENDPOINT: 'https://graph.microsoft.com'
};

class AzureADUserFetcher {
    constructor() {
        this.clientId = process.env.USER_CLIENT_ID || ENV_VARS.USER_CLIENT_ID;
        this.clientSecret = process.env.USER_CLIENT_SECRET || ENV_VARS.USER_CLIENT_SECRET;
        this.tenantId = process.env.USER_TENANT_ID || ENV_VARS.USER_TENANT_ID;
        this.graphEndpoint = process.env.USER_GRAPH_ENDPOINT || ENV_VARS.USER_GRAPH_ENDPOINT;
        
        // Validate required environment variables
        if (!this.clientId || !this.clientSecret || !this.tenantId) {
            console.error('Error: Missing required environment variables');
            process.exit(1);
        }
        
        this.accessToken = null;
        this.tokenExpiresAt = 0;
    }

    /**
     * Make HTTP request
     */
    makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            };

            const req = https.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.error?.message || data}`));
                        }
                    } catch (e) {
                        reject(new Error(`Failed to parse JSON: ${data}`));
                    }
                });
            });

            req.on('error', (err) => {
                reject(err);
            });

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    }

    /**
     * Acquire access token using client credentials flow
     */
    async getAccessToken() {
        try {
            // Check if we have a valid cached token
            if (this.accessToken && Date.now() < this.tokenExpiresAt) {
                return this.accessToken;
            }

            const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
            
            const body = new URLSearchParams({
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: `${this.graphEndpoint}/.default`,
                grant_type: 'client_credentials'
            }).toString();

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Content-Length': Buffer.byteLength(body)
                },
                body: body
            };

            const tokenData = await this.makeRequest(tokenUrl, options);
            this.accessToken = tokenData.access_token;

            // Calculate token expiration (subtract 5 minutes for safety)
            const expiresIn = tokenData.expires_in || 3600;
            this.tokenExpiresAt = Date.now() + (expiresIn - 300) * 1000;

            return this.accessToken;

        } catch (error) {
            console.error('Error acquiring access token:', error.message);
            throw error;
        }
    }

    /**
     * Make authenticated request to Microsoft Graph API
     */
    async makeGraphRequest(url) {
        try {
            const token = await this.getAccessToken();
            
            const options = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'ConsistencyLevel':'eventual'
                }
            };

            return await this.makeRequest(url, options);

        } catch (error) {
            console.error('Graph API request failed:', error.message);
            throw error;
        }
    }
    

    /**
     * Fetch users from a specific Azure AD group
     */
    async fetchUsersFromGroup(groupsIds, pageSize = 999) {
        const allUsers = [];
        
        try {
            for (const groupId of groupsIds) {
                let nextLink = null;
                
                // Build initial URL for group members
                const baseUrl = `${this.graphEndpoint}/v1.0/groups/${groupId}/members`;
                const params = [
                    '$select=id,displayName,mail,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,createdDateTime,lastSignInDateTime,userType',
                    `$top=${pageSize}`,
                    '$orderby=displayName'
                ];
                let url = `${baseUrl}?${params.join('&')}`;

                while (true) {

                    // Use next link if available, otherwise use constructed URL
                    const currentUrl = nextLink || url;

                    //TODO: Add retry logic and a a notification in case of failing
                    const response = await this.makeGraphRequest(currentUrl);

                    // Extract users from response (filter only user objects, not other member types)
                    const members = response.value || [];
                    const users = members.filter(member => member['@odata.type'] === '#microsoft.graph.user' || !member['@odata.type']);
                    allUsers.push(...users);

                    console.log(`   Retrieved ${users.length} users from ${members.length} members (Total users so far: ${allUsers.length})`);

                    // Check for next page
                    nextLink = response['@odata.nextLink'];
                    if (!nextLink) {
                        break;
                    }

                    // Small delay to avoid rate limiting
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                console.log(`\n✅ Successfully retrieved ${allUsers.length} total users from group`);
            }
            return allUsers;

        } catch (error) {
            //TODO: Add retry logic and a a notification in case of failing

            console.error('❌ Error fetching users from group:', error.message);
            throw error;
        }
    }

        /**
     * Get groups for a specific user by their ID
     */
    async getUserGroupsById(userId) {
        try {
            const url = `${this.graphEndpoint}/v1.0/users/${userId}/memberOf?$select=id,displayName,groupTypes,securityEnabled,mailEnabled,description`;
            const response = await this.makeGraphRequest(url);
            return response.value || [];
        } catch (error) {
            console.error(`Error fetching groups for user ${userId}:`, error.message);
            throw error;
        }
    }

    /**
     * Fetch all users from Azure AD with pagination
     */
    async fetchAllUsers(pageSize = 999) {
        const allUsers = [];
        let nextLink = null;
        let pageCount = 0;

        try {
            console.log('🔄 Starting to fetch all users from Azure AD...');

            // Build initial URL
            const baseUrl = `${this.graphEndpoint}/v1.0/users`;
            const params = [
                '$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,businessPhones,mobilePhone,createdDateTime,lastSignInDateTime,userType',
                `$top=${pageSize}`,
                '$orderby=displayName'
            ];
            let url = `${baseUrl}?${params.join('&')}`;

            while (true) {
                pageCount++;
                console.log(`📄 Fetching page ${pageCount}...`);

                // Use next link if available, otherwise use constructed URL
                const currentUrl = nextLink || url;
                const response = await this.makeGraphRequest(currentUrl);

                // Extract users from response
                const users = response.value || [];
                allUsers.push(...users);

                console.log(`   Retrieved ${users.length} users (Total so far: ${allUsers.length})`);

                // Check for next page
                nextLink = response['@odata.nextLink'];
                if (!nextLink) {
                    break;
                }

                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            console.log(`\n✅ Successfully retrieved ${allUsers.length} total users from Azure AD`);
            return allUsers;

        } catch (error) {
            console.error('❌ Error fetching users:', error.message);
            return allUsers; // Return what we have so far
        }
    }

    /**
     * Get group information with authorization details
     */
    async getGroupInfo(groupId) {
        try {
            const url = `${this.graphEndpoint}/v1.0/groups/${groupId}?$select=id,displayName,description,groupTypes,securityEnabled,mailEnabled,createdDateTime,visibility,membershipRule,membershipRuleProcessingState`;
            const group = await this.makeGraphRequest(url);
            return group;
        } catch (error) {
            console.error('❌ Error fetching group info:', error.message);
            return null;
        }
    }

    /**
     * Get applications that this group has access to
     */
    async getGroupApplicationAccess(groupId) {
        try {
            const url = `${this.graphEndpoint}/v1.0/groups/${groupId}/appRoleAssignments`;
            const assignments = await this.makeGraphRequest(url);
            return assignments.value || [];
        } catch (error) {
            console.error('❌ Error fetching group app assignments:', error.message);
            return [];
        }
    }

    /**
     * Extract all email aliases for a user
     */
    async extractUserAliases(user) {
        try {
        const aliases = new Set();

        // Add UPN
        if (user.userPrincipalName) {
            aliases.add(user.userPrincipalName.toLowerCase());
        }

        // Extract from proxy addresses
        const proxyAddresses = user.proxyAddresses || [];
        for (const proxy of proxyAddresses) {
            if (typeof proxy === 'string' && (proxy.startsWith('smtp:') || proxy.startsWith('SMTP:'))) {
                const email = proxy.substring(5); // Remove 'smtp:' or 'SMTP:' prefix
                aliases.add(email.toLowerCase());
            }
        }

        
        const groups = await this.getUserGroupsById(user.id);

        return {
            id: user.id,
            displayName: user.displayName,
            primaryEmail: user.mail,
            accountEnabled: user.accountEnabled,
            userType: user.userType,
            department: user.department,
            jobTitle: user.jobTitle,
            createdDateTime: user.createdDateTime,
            lastSignInDateTime: user.lastSignInDateTime,
            aliases: Array.from(aliases).sort(),
            groups: groups.map(g => ({
                id: g.id,
                name: g.displayName
            }))
        };
        } catch (error) {
            console.error('❌ Error extracting user aliases:', error.message);
            throw error;
        }
    }

    /**
     * Fetch users from a specific group and process their aliases
     */
    async processGroupUsers(groupsIds) {
        try {
        console.log(`🚀 Fetching users from group ${groupsIds} with their email aliases...`);

        // Get users from the group
        const allUsers = await this.fetchUsersFromGroup(groupsIds);

        // Process each user to extract aliases and groups
        const usersWithAliases = [];
        console.log(`\n🔄 Processing ${allUsers.length} users to extract aliases and groups...`);

        for (let user of allUsers) {

            const userData = await this.extractUserAliases(user);
            
            usersWithAliases.push(userData);
        }

        // FILTER USERS: Only keep users with MATRIX or Content_Access_Sensitive groups
        const filteredUsers = this.filterUsersWithMatrixOrSensitiveGroups(usersWithAliases);

        console.log(`✅ Processed all ${usersWithAliases.length} users from group`);
        console.log(`🔍 Filtered to ${filteredUsers.length} users with MATRIX or Content_Access_Sensitive groups`);
        
        return filteredUsers; // Return filtered users instead of all users
        } catch (error) {
            console.error('❌ Error processing group users:', error.message);
            throw error;
        }
    }

    filterUsersWithMatrixOrSensitiveGroups(users) {
        return users.filter(user => {
            return user.groups && user.groups.some(group => {
                const groupName = group.name || '';
                return groupName.startsWith('MATRIX') || groupName === 'Content_Access_Sensitive';
            });
        });
    }

    /**
     * Fetch all users and process their aliases
     */
    async processAllUsers() {
        console.log('🚀 Fetching all users with their email aliases...');

        // Get all users
        const allUsers = await this.fetchAllUsers();

        if (!allUsers.length) {
            console.log('❌ No users found or error occurred');
            return [];
        }

        // Process each user to extract aliases
        const usersWithAliases = [];
        console.log(`\n🔄 Processing ${allUsers.length} users to extract aliases...`);

        for (let i = 0; i < allUsers.length; i++) {
            if ((i + 1) % 100 === 0) {
                console.log(`   Processed ${i + 1}/${allUsers.length} users...`);
            }

            const userData = await this.extractUserAliases(allUsers[i]);
            usersWithAliases.push(userData);
        }

        console.log(`✅ Processed all ${usersWithAliases.length} users`);
        return usersWithAliases;
    }

    /**
     * Save users data to file in specified format
     */
    async saveToFile(usersData, filename, formatType = 'json') {
        try {
            if (formatType.toLowerCase() === 'json') {
                const jsonData = JSON.stringify(usersData, null, 2);
                fs.writeFileSync(filename, jsonData, 'utf8');
                console.log(`✅ Results saved to ${filename}`);

            } else if (formatType.toLowerCase() === 'csv') {
                if (!usersData.length) {
                    console.log('❌ No data to save');
                    return;
                }

                // Create CSV content
                const headers = [
                    'ID', 'Display Name', 'Primary Email', 'UPN', 'Account Enabled',
                    'User Type', 'Department', 'Job Title', 'Office Location',
                    'Business Phones', 'Mobile Phone', 'Created Date', 'Last Sign In',
                    'Alias Count', 'All Aliases', 'Proxy Addresses'
                ];

                let csvContent = headers.join(',') + '\n';

                for (const user of usersData) {
                    const row = [
                        this.escapeCsvField(user.id || ''),
                        this.escapeCsvField(user.displayName || ''),
                        this.escapeCsvField(user.primaryEmail || ''),
                        this.escapeCsvField(user.userPrincipalName || ''),
                        user.accountEnabled || '',
                        this.escapeCsvField(user.userType || ''),
                        this.escapeCsvField(user.department || ''),
                        this.escapeCsvField(user.jobTitle || ''),
                        this.escapeCsvField(user.officeLocation || ''),
                        this.escapeCsvField((user.businessPhones || []).join('; ')),
                        this.escapeCsvField(user.mobilePhone || ''),
                        this.escapeCsvField(user.createdDateTime || ''),
                        this.escapeCsvField(user.lastSignInDateTime || ''),
                        user.aliasCount || 0,
                        this.escapeCsvField((user.aliases || []).join('; ')),
                        this.escapeCsvField((user.proxyAddresses || []).join('; '))
                    ];
                    csvContent += row.join(',') + '\n';
                }

                fs.writeFileSync(filename, csvContent, 'utf8');
                console.log(`✅ Results saved to ${filename}`);
            }

        } catch (error) {
            console.error('❌ Error saving to file:', error.message);
        }
    }

    /**
     * Escape CSV field if it contains special characters
     */
    escapeCsvField(field) {
        if (typeof field !== 'string') {
            return field;
        }
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
            return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
    }

    /**
     * Print a summary of users and their aliases
     */
    printSummary(usersData) {
        if (!usersData.length) {
            console.log('❌ No users to summarize');
            return;
        }

        console.log('\n' + '='.repeat(80));
        console.log('📊 USER SUMMARY');
        console.log('='.repeat(80));
        console.log(`Total Users: ${usersData.length}`);

        // Count statistics
        const enabledUsers = usersData.filter(u => u.accountEnabled).length;
        const guestUsers = usersData.filter(u => u.userType === 'Guest').length;
        const usersWithMultipleAliases = usersData.filter(u => (u.aliasCount || 0) > 1).length;
        const totalAliases = usersData.reduce((sum, u) => sum + (u.aliasCount || 0), 0);

        console.log(`Enabled Users: ${enabledUsers}`);
        console.log(`Disabled Users: ${usersData.length - enabledUsers}`);
        console.log(`Guest Users: ${guestUsers}`);
        console.log(`Users with Multiple Aliases: ${usersWithMultipleAliases}`);
        console.log(`Total Email Aliases: ${totalAliases}`);
        console.log(`Average Aliases per User: ${(totalAliases / usersData.length).toFixed(2)}`);

        // Show users with most aliases
        console.log('\n' + '='.repeat(80));
        console.log('🏆 TOP 10 USERS WITH MOST ALIASES');
        console.log('='.repeat(80));

        const sortedUsers = usersData
            .sort((a, b) => (b.aliasCount || 0) - (a.aliasCount || 0))
            .slice(0, 10);

        for (let i = 0; i < sortedUsers.length; i++) {
            const user = sortedUsers[i];
            const displayName = (user.displayName || 'N/A').padEnd(40);
            console.log(`${(i + 1).toString().padStart(2)}. ${displayName} (${user.aliasCount || 0} aliases)`);
            
            const aliases = user.aliases || [];
            for (let j = 0; j < Math.min(3, aliases.length); j++) {
                console.log(`     📧 ${aliases[j]}`);
            }
            if (aliases.length > 3) {
                console.log(`     ... and ${aliases.length - 3} more`);
            }
            console.log();
        }
    }
}

/**
 * Print help information
 */
function printHelp() {
    console.log(`
🔍 Azure AD User Fetcher - Help

Usage:
    node fetch_all_users.js [OPTIONS] [FILENAME]

Options:
    --all               Fetch ALL users from Azure AD (default)
    --group GROUP_ID    Fetch users from specific group only
    --json, -j          Save as JSON format (default)
    --csv, -c           Save as CSV format
    --help, -h          Show this help message

Examples:
    # Fetch all users and save as JSON (default)
    node fetch_all_users.js

    # Fetch users from specific group
    node fetch_all_users.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436

    # Fetch group users and save as CSV
    node fetch_all_users.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436 --csv

    # Save to custom filename
    node fetch_all_users.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436 my_group_users.json

Features:
    ✅ Fetches ALL users from Azure AD OR users from specific group
    ✅ Extracts all email aliases (mail, UPN, proxy addresses)
    ✅ Provides detailed user information
    ✅ Supports JSON and CSV output formats
    ✅ Shows summary statistics
    ✅ Handles rate limiting and errors gracefully
    `);
}

/**
 * Main function
 */
async function main() {
    console.log('🔍 Azure AD User Fetcher - Get users with email aliases');
    console.log('='.repeat(60));

    // Parse command line arguments
    const args = process.argv.slice(2);
    let outputFormat = 'json';
    let outputFile = null;
    let groupsIds = null;

    // Parse arguments
    for (let i = 0; i < args.length; i++) {
        const arg = args[i].toLowerCase();

        if (arg === '--help' || arg === '-h') {
            printHelp();
            return;
        } else if (arg === '--csv' || arg === '-c') {
            outputFormat = 'csv';
        } else if (arg === '--json' || arg === '-j') {
            outputFormat = 'json';
        } else if (arg === '--group') {
            if (i + 1 < args.length) {
                if (!groupsIds) {
                    groupsIds = [];
                } else if (typeof groupsIds === 'string') {
                    groupsIds = [groupsIds];
                }
                groupsIds.push(args[i + 1]);
                i++; 
            } else {
                console.error('❌ Error: --group requires a group ID');
                process.exit(1);
            }
        } else if (arg === '--all') {
            groupsIds = null;
        } else if (!arg.startsWith('--') && !outputFile) {
            // This is a filename
            outputFile = args[i];
            if (outputFile.endsWith('.csv')) {
                outputFormat = 'csv';
            } else if (outputFile.endsWith('.json')) {
                outputFormat = 'json';
            }
        }
    }

    // Set default output filename if not specified
    if (!outputFile) {
        if (groupsIds) {
            outputFile = `group_${groupsIds[0].substring(0, 8)}_users_filterd`;
        } else {
            outputFile = 'all_users_with_aliases';
        }
        outputFile += outputFormat === 'csv' ? '.csv' : '.json';
    }

    try {
        // Initialize the fetcher
        const fetcher = new AzureADUserFetcher();

        let usersData;
        if (groupsIds) {
            console.log(`🎯 Target Group ID: ${groupsIds}`);
            // Fetch users from specific group
            usersData = await fetcher.processGroupUsers(groupsIds);
        } else {
            // Fetch all users
            usersData = await fetcher.processAllUsers();
        }

        if (usersData.length > 0) {
            // Save to file
            await fetcher.saveToFile(usersData, outputFile, outputFormat);

            // Print summary
            fetcher.printSummary(usersData);

            console.log(`\n🎉 Complete! Check '${outputFile}' for detailed results.`);
        } else {
            console.log('❌ No users were retrieved.');
        }

    } catch (error) {
        console.error('❌ Fatal error:', error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = { AzureADUserFetcher };



/*

1- Remove content access to users in AD that has 'Content Access' and add Matrix groups
2- Fetch users from AD (X) Fetch users from our DB (Y).
    2.1- X-Y = Users in AD but not in DB -> Add to DB
    2.2- Y-X = Users in DB but not in AD -> Remove from DB
    2.3- Users in AD that has no Content Access -> Take a note about them & remove from DB if exist
    2.4- Users in DB But they have no Content Access in AD -> Remove from DB

3- Add UUID to V2.
4- When a user logIN, For any auth used (AD, UAE Pass):
    4.1- Get email from token and fetch from V2 by email, If not found, go to 4.2
    4.2- Get ID from token and fetch from V2 by ID, 
    4.3- Get the UUID from the record and add it to the token.
    4.4- This UUID will be used as a user identifier, but not the email
*/