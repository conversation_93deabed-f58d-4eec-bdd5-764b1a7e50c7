#!/usr/bin/env node

/**
 * Azure AD User Fetcher - Enhanced version with group filtering
 * Fetches users from Azure AD groups with email aliases and group memberships
 */

require('dotenv').config();
const https = require('https');
const fs = require('fs');

class AzureADUserFetcher {
    constructor() {
        this.clientId = process.env.USER_CLIENT_ID;
        this.clientSecret = process.env.USER_CLIENT_SECRET;
        this.tenantId = process.env.USER_TENANT_ID;
        this.graphEndpoint = process.env.USER_GRAPH_ENDPOINT || 'https://graph.microsoft.com';

        if (!this.clientId || !this.clientSecret || !this.tenantId) {
            throw new Error('Missing required environment variables: USER_CLIENT_ID, USER_CLIENT_SECRET, USER_TENANT_ID');
        }
        
        this.accessToken = null;
        this.tokenExpiresAt = 0;
    }

    /**
     * Make HTTP request
     */
    makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            };

            const req = https.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.error?.message || data}`));
                        }
                    } catch (e) {
                        reject(new Error(`Failed to parse JSON: ${data}`));
                    }
                });
            });

            req.on('error', (err) => {
                reject(err);
            });

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    }

    /**
     * Acquire access token using client credentials flow
     */
    async getAccessToken() {
        try {
            if (this.accessToken && Date.now() < this.tokenExpiresAt) {
                return this.accessToken;
            }

            const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
            
            const body = new URLSearchParams({
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: `${this.graphEndpoint}/.default`,
                grant_type: 'client_credentials'
            }).toString();

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Content-Length': Buffer.byteLength(body)
                },
                body: body
            };

            const tokenData = await this.makeRequest(tokenUrl, options);
            this.accessToken = tokenData.access_token;

            const expiresIn = tokenData.expires_in || 3600;
            this.tokenExpiresAt = Date.now() + (expiresIn - 300) * 1000;

            return this.accessToken;
        } catch (error) {
            console.error(`Error acquiring access token: ${error.message}`);
            throw error;
        }
    }

    /**
     * Make authenticated request to Microsoft Graph API
     */
    async makeGraphRequest(url) {
        try {
            const token = await this.getAccessToken();
            
            const options = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'ConsistencyLevel': 'eventual'
                }
            };

            return await this.makeRequest(url, options);

        } catch (error) {
            console.error('Graph API request failed:', error.message);
            throw error;
        }
    }

    /**
     * Fetch users from specific Azure AD groups
     */
    async fetchUsersFromGroup(groupsIds, pageSize = 999) {
        const allUsers = [];
        
        try {
            for (const groupId of groupsIds) {
                let nextLink = null;
                
                const baseUrl = `${this.graphEndpoint}/v1.0/groups/${groupId}/members`;
                const params = [
                    '$select=id,displayName,mail,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,createdDateTime,lastSignInDateTime,userType',
                    `$top=${pageSize}`,
                    '$orderby=displayName'
                ];
                let url = `${baseUrl}?${params.join('&')}`;

                while (true) {
                    const currentUrl = nextLink || url;
                    const response = await this.makeGraphRequest(currentUrl);

                    const members = response.value || [];
                    const users = members.filter(member => member['@odata.type'] === '#microsoft.graph.user' || !member['@odata.type']);
                    allUsers.push(...users);

                    console.log(`   Retrieved ${users.length} users from ${members.length} members (Total users so far: ${allUsers.length})`);

                    nextLink = response['@odata.nextLink'];
                    if (!nextLink) {
                        break;
                    }

                    // Small delay to avoid rate limiting
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            console.log(`\n✅ Successfully retrieved ${allUsers.length} total users from ${groupsIds.length} groups`);
            return allUsers;

        } catch (error) {
            console.error('❌ Error fetching users from groups:', error.message);
            return allUsers;
        }
    }

    /**
     * Get groups for a specific user by their ID
     */
    async getUserGroupsById(userId) {
        try {
            const url = `${this.graphEndpoint}/v1.0/users/${userId}/memberOf?$select=id,displayName,groupTypes,securityEnabled,mailEnabled,description`;
            const response = await this.makeGraphRequest(url);
            return response.value || [];
        } catch (error) {
            console.error(`Error fetching groups for user ${userId}:`, error.message);
            return [];
        }
    }

    /**
     * Extract all email aliases for a user
     */
    async extractUserAliases(user) {
        try {
            const aliases = new Set();

            if (user.userPrincipalName) {
                aliases.add(user.userPrincipalName.toLowerCase());
            }

            const proxyAddresses = user.proxyAddresses || [];
            for (const proxy of proxyAddresses) {
                if (typeof proxy === 'string' && (proxy.startsWith('smtp:') || proxy.startsWith('SMTP:'))) {
                    const email = proxy.substring(5);
                    aliases.add(email.toLowerCase());
                }
            }

            const groups = await this.getUserGroupsById(user.id);

            return {
                id: user.id,
                displayName: user.displayName,
                primaryEmail: user.mail,
                accountEnabled: user.accountEnabled,
                userType: user.userType,
                department: user.department,
                jobTitle: user.jobTitle,
                createdDateTime: user.createdDateTime,
                lastSignInDateTime: user.lastSignInDateTime,
                aliases: Array.from(aliases).sort(),
                groups: groups.map(g => ({
                    id: g.id,
                    name: g.displayName
                }))
            };
        } catch (error) {
            console.error('❌ Error extracting user aliases:', error.message);
            throw error;
        }
    }

    /**
     * Fetch users from specific groups and process their aliases
     */
    async processGroupUsers(groupsIds) {
        try {
            // Ensure groupsIds is an array
            const groupsArray = Array.isArray(groupsIds) ? groupsIds : [groupsIds];
            
            console.log(`🚀 Fetching users from ${groupsArray.length} group(s) with their email aliases...`);
            console.log(`   Groups: ${groupsArray.join(', ')}`);

            // Get users from the groups
            const allUsers = await this.fetchUsersFromGroup(groupsArray);

            // Process each user to extract aliases and groups
            const usersWithAliases = [];
            console.log(`\n🔄 Processing ${allUsers.length} users to extract aliases and groups...`);

            for (let user of allUsers) {
                const userData = await this.extractUserAliases(user);
                usersWithAliases.push(userData);
            }

            // FILTER USERS: Only keep users with MATRIX or Content_Access_Sensitive groups
            const filteredUsers = [];
            const excludedUserIds = [];

            usersWithAliases.forEach(user => {
                const hasTargetGroup = user.groups && user.groups.some(group => {
                    const groupName = group.name || '';
                    return groupName.startsWith('MATRIX') || groupName === 'Content_Access_Sensitive';
                });

                if (hasTargetGroup) {
                    filteredUsers.push(user);
                } else {
                    excludedUserIds.push(user.id);
                    console.log(`❌ Excluded User ID: ${user.id} (${user.displayName || 'No Name'}) - No MATRIX or Content_Access_Sensitive groups`);
                }
            });

            console.log(`✅ Processed all ${usersWithAliases.length} users from groups`);
            console.log(`🔍 Filtered to ${filteredUsers.length} users with MATRIX or Content_Access_Sensitive groups`);
            console.log(`❌ Excluded ${excludedUserIds.length} users without target groups`);
            
            // Log all excluded user IDs in a single line for easy copying
            if (excludedUserIds.length > 0) {
                console.log(`\n📋 Excluded User IDs: ${excludedUserIds.join(', ')}`);
            }
            
            return filteredUsers;
            
        } catch (error) {
            console.error('❌ Error processing group users:', error.message);
            throw error;
        }
    }

    /**
     * Save users data to file in specified format
     */
    async saveToFile(usersData, filename, formatType = 'json') {
        try {
            if (formatType.toLowerCase() === 'json') {
                const jsonData = JSON.stringify(usersData, null, 2);
                fs.writeFileSync(filename, jsonData, 'utf8');
                console.log(`✅ Results saved to ${filename}`);

            } else if (formatType.toLowerCase() === 'csv') {
                if (!usersData.length) {
                    console.log('❌ No data to save');
                    return;
                }

                const headers = [
                    'ID', 'Display Name', 'Primary Email', 'Account Enabled',
                    'User Type', 'Department', 'Job Title', 'Created Date', 'Last Sign In',
                    'Alias Count', 'All Aliases', 'Group Count', 'Group Names'
                ];

                let csvContent = headers.join(',') + '\n';

                for (const user of usersData) {
                    const row = [
                        this.escapeCsvField(user.id || ''),
                        this.escapeCsvField(user.displayName || ''),
                        this.escapeCsvField(user.primaryEmail || ''),
                        user.accountEnabled || '',
                        this.escapeCsvField(user.userType || ''),
                        this.escapeCsvField(user.department || ''),
                        this.escapeCsvField(user.jobTitle || ''),
                        this.escapeCsvField(user.createdDateTime || ''),
                        this.escapeCsvField(user.lastSignInDateTime || ''),
                        (user.aliases || []).length,
                        this.escapeCsvField((user.aliases || []).join('; ')),
                        (user.groups || []).length,
                        this.escapeCsvField((user.groups || []).map(g => g.name).join('; '))
                    ];
                    csvContent += row.join(',') + '\n';
                }

                fs.writeFileSync(filename, csvContent, 'utf8');
                console.log(`✅ Results saved to ${filename}`);
            }
        } catch (error) {
            console.error(`❌ Error saving to file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Escape CSV field
     */
    escapeCsvField(field) {
        if (typeof field !== 'string') return field;
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
            return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
    }

    /**
     * Print summary statistics
     */
    printSummary(usersData) {
        if (!usersData.length) {
            console.log('❌ No users to summarize');
            return;
        }

        console.log('\n' + '='.repeat(80));
        console.log('📊 USER SUMMARY STATISTICS');
        console.log('='.repeat(80));

        const enabledUsers = usersData.filter(u => u.accountEnabled !== false).length;
        const guestUsers = usersData.filter(u => u.userType === 'Guest').length;
        const usersWithMultipleAliases = usersData.filter(u => (u.aliases || []).length > 1).length;
        const totalAliases = usersData.reduce((sum, u) => sum + (u.aliases || []).length, 0);

        console.log(`Total Users: ${usersData.length}`);
        console.log(`Enabled Users: ${enabledUsers}`);
        console.log(`Disabled Users: ${usersData.length - enabledUsers}`);
        console.log(`Guest Users: ${guestUsers}`);
        console.log(`Users with Multiple Aliases: ${usersWithMultipleAliases}`);
        console.log(`Total Email Aliases: ${totalAliases}`);
        console.log(`Average Aliases per User: ${(totalAliases / usersData.length).toFixed(2)}`);
    }
}

/**
 * Print help information
 */
function printHelp() {
    console.log(`
🔍 Azure AD User Fetcher - Help

Usage:
    node fetchAdUsers.js [OPTIONS] [FILENAME]

Options:
    --group <groupId>     Fetch users from specific group (can be used multiple times)
    --csv, -c            Output in CSV format (default: JSON)
    --json, -j           Output in JSON format
    --help, -h           Show this help message

Examples:
    # Fetch users from single group
    node fetchAdUsers.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436

    # Fetch users from multiple groups
    node fetchAdUsers.js --group ad85f6c4-04af-48ad-aed6-c63e7f9f768d --group a0d83e83-22c0-4406-b0b8-6eb7246aa436

    # Fetch users and save as CSV
    node fetchAdUsers.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436 --csv

    # Save to custom filename
    node fetchAdUsers.js --group a0d83e83-22c0-4406-b0b8-6eb7246aa436 my_users.json

Features:
    ✅ Fetches users from specific Azure AD groups
    ✅ Extracts all email aliases (mail, UPN, proxy addresses)
    ✅ Gets user group memberships
    ✅ Filters users with MATRIX or Content_Access_Sensitive groups
    ✅ Supports JSON and CSV output formats
    ✅ Shows summary statistics
    ✅ Logs excluded users without target groups
    `);
}

/**
 * Main function
 */
async function main() {
    console.log('🔍 Azure AD User Fetcher - Get users with email aliases and group filtering');
    console.log('='.repeat(80));

    // Parse command line arguments
    const args = process.argv.slice(2);
    let outputFormat = 'json';
    let outputFile = null;
    let groupsIds = null;

    // Parse arguments
    for (let i = 0; i < args.length; i++) {
        const arg = args[i].toLowerCase();

        if (arg === '--help' || arg === '-h') {
            printHelp();
            return;
        } else if (arg === '--csv' || arg === '-c') {
            outputFormat = 'csv';
        } else if (arg === '--json' || arg === '-j') {
            outputFormat = 'json';
        } else if (arg === '--group') {
            if (i + 1 < args.length) {
                if (!groupsIds) {
                    groupsIds = [];
                } else if (typeof groupsIds === 'string') {
                    groupsIds = [groupsIds];
                }
                groupsIds.push(args[i + 1]);
                i++; 
            } else {
                console.error('❌ Error: --group requires a group ID');
                process.exit(1);
            }
        } else if (!arg.startsWith('--') && !outputFile) {
            outputFile = args[i];
            if (outputFile.endsWith('.csv')) {
                outputFormat = 'csv';
            } else if (outputFile.endsWith('.json')) {
                outputFormat = 'json';
            }
        }
    }

    if (!groupsIds || groupsIds.length === 0) {
        console.error('❌ Error: At least one group ID is required. Use --group <groupId>');
        console.log('Use --help for usage information');
        process.exit(1);
    }

    // Set default output filename if not specified
    if (!outputFile) {
        if (groupsIds.length === 1) {
            outputFile = `group_${groupsIds[0].substring(0, 8)}_users`;
        } else {
            outputFile = `multiple_groups_users`;
        }
        outputFile += outputFormat === 'csv' ? '.csv' : '.json';
    }

    try {
        // Convert string to array
        let groupsArray;
        if (typeof groupsIds === 'string') {
            if (groupsIds.includes(',')) {
                groupsArray = groupsIds.split(',').map(id => id.trim());
            } else {
                groupsArray = [groupsIds];
            }
        } else {
            groupsArray = groupsIds;
        }
        
        console.log(`🎯 Target Group ID(s): ${groupsArray.join(', ')}`);

        // Initialize the fetcher
        const fetcher = new AzureADUserFetcher();

        // Fetch users from specific groups
        const usersData = await fetcher.processGroupUsers(groupsArray);

        if (usersData.length > 0) {
            // Save to file
            await fetcher.saveToFile(usersData, outputFile, outputFormat);

            // Print summary
            fetcher.printSummary(usersData);

            console.log(`\n🎉 Complete! Check '${outputFile}' for detailed results.`);
        } else {
            console.log('❌ No users were retrieved or all users were filtered out.');
        }

    } catch (error) {
        console.error('❌ Fatal error:', error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = { AzureADUserFetcher };