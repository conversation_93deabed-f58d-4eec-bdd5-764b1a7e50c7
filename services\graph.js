const crypto = require('crypto');
const axios = require('axios');
const auth = require('./auth');
const { setUserActivationStatus } = require('./executeQuery.service');
const constants = require('../config/constants.json');
const logger = require('./logger'); // Import the logger
const { encryptEmail } = require('./encryption.service');

async function getAuthToken(){
    let authCacheKey = crypto.createHash('md5').update('authCache').digest("hex");
    logger.info(`Generating auth cache key: ${authCacheKey}`);
    
    let authData = await auth.getToken(auth.tokenRequest);
    let accessToken = authData.accessToken;
    logger.info('Obtained access token');

    return accessToken;
}

/**
 * Gets users under a group
 */
async function getGroupUsers(){
    try {
        const authToken = await getAuthToken();
        if(!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }};
        const userObjectFields = 'id,displayName,mail,createdDateTime,department';
        const groupMatrixInfo = require("./helpers/allGroupMatrix.json");
        let groupUsers = {};

        for (const group of Object.keys(groupMatrixInfo)) {
            try {
                logger.info(`Fetching members for group: ${group}`);
                const response = await axios.get(`${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups/${group}/members?$top=999&$select=${userObjectFields}`, options);
                groupUsers[group] = response.data.value;
                logger.info(`Fetched ${response.data.value.length} members for group ${group}`);
            } catch (exp) {
                if (exp.response && exp.response.status && exp.response.status == 404) {
                    logger.warn(`The group ${group} is not found`);
                } else {
                    logger.error(`Error fetching members for group ${group}: ${exp.message}`);
                }
            }
        }

        groupUsers = Object.entries(groupUsers).map(([group, members]) => {
            return {
                id: group,
                name: groupMatrixInfo[group],
                members: members.map(member => {
                    return {
                        id: member.id,
                        name: member.displayName,
                        email: member.mail,
                        department: member.department,
                        createdDateTime: member.createdDateTime,
                        encEmail: member.mail ? encryptEmail(member.mail) : null
                    };
                })
            };
        });
        
        logger.info('Fetched all group users successfully');
        return groupUsers;
    } catch (error) {
        logger.error(`Failed to get group users: ${error.message}`);
        throw error;
    }
}

// Check if a user exists in Azure Active Directory
async function checkUserInActiveDirectory(userEmail) {
    try {
        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        logger.info(`Checking if user ${userEmail} is part of Active Directory`);
        const response = await axios.get(`${process.env.USER_GRAPH_ENDPOINT}/v1.0/users/${userEmail}`, options);

        if (response.status === 200) {
            logger.info(`User ${userEmail} is part of Active Directory`);
            return response.data;
        }
    } catch (error) {
        if (error.response && error.response.status === 404) {
            logger.info(`User ${userEmail} is not part of Active Directory`);
            return null;  // User does not exist
        }
        logger.error(`Error checking user ${userEmail}: ${error.message}`);
        throw error;
    }
}

/**
 * Invites a user to Microsoft Entra ID 
 */
async function inviteEntraIdUser(user){
    try {
        const authToken = await getAuthToken();
        if(!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }};

        const data = {
          invitedUserEmailAddress: user,
          inviteRedirectUrl: process.env.PLATFORM_BASEPATH,
          sendInvitationMessage: true
        };

        logger.info(`Inviting user: ${user}`);
        const response = await axios.post(`${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.invitations}`, data, options);
        logger.info(`[ENTRAIDINVITER CRON] User ${user} has been invited`);
        const encryptedEmail = encryptEmail(user);
        await setUserActivationStatus(encryptedEmail, 'ENTRA_ID_INVITE_SENT');
        
        logger.info(`User activation status updated for user: ${user}`);
        return response.data;
    } catch (error) {
        logger.error(`Failed to invite user ${user}: ${error.message}`);
        throw error;
    }
}

async function inviteStatusEntraIdUser(user) {
    try {
        logger.info(`Checking invite status for user: ${user}`);

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$select=Id,userPrincipalName,userType,displayName,mail,accountEnabled,creationType,externalUserState&$filter=mail eq '${user}'`,
            options
        );

        let userData = response.data.value.length ? response.data.value[0] : null;
        if (userData) {
            logger.info(`User data found for ${user}: ${JSON.stringify(userData)}`);
            if (userData.externalUserState === 'Accepted') {
                return {
                    id: userData.id,
                    mail: userData.mail,
                    displayName: userData.displayName,
                    inviteStatus: true,
                    accountEnabled: userData.accountEnabled
                };
            } else {
                return {
                    id: userData.id,
                    mail: userData.mail,
                    displayName: userData.displayName,
                    inviteStatus: false,
                    accountEnabled: userData.accountEnabled
                };
            }
        } else {
            logger.warn(`No user data found for ${user}`);
            throw new Error(`No user data found for ${user}`);
        }
    } catch (error) {
        logger.error(`Error checking invite status for user ${user}: ${error.message}`);
        throw error;
    }
}

async function getUserGroups(user_id) {
    try {
        logger.info('Fetching user groups.', { user_id });

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.', { user_id });

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${user_id}/memberOf`,
            options
        );

        logger.info('User groups fetched successfully.', { user_id, groupCount: response.data.value.length });
        return response.data.value;

    } catch (exp) {
        logger.error(`Error fetching user groups for user ${user_id}: ${exp.message}`, { user_id });
        throw exp;
    }
}


async function assignUserToGroup(user_id, groups) {
    try {
        let assignedGroups = []
        logger.info('Assigning user to groups.', { user_id, groups });

        let userGroups = await getUserGroups(user_id);
        logger.debug('Fetched current user groups.', { user_id, userGroups });

        userGroups.forEach(group => {
            groups = groups.filter(g => g != group.id);
        });

        if (!groups.length) {
            logger.info('User is already assigned to the given groups.', { user_id, groups });
            return {
                status: true,
                message: 'The user is already assigned to the given groups'
            };
        }

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.');

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const groupPromises = [];
        for (const group of groups) {
            const data = {
                "@odata.id": `https://graph.microsoft.com/v1.0/users/${user_id}`
            };
            groupPromises.push(
                axios.post(
                    `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group}/members/$ref`,
                    data,
                    options
                )
            );
        }

        const response = await Promise.all(groupPromises);
        logger.info(`User ${user_id} has been assigned to groups: [${groups.join(',')}]`, { user_id, groups });

        return {
            status: true,
            message: `User has been assigned to [${groups.join(',')}]`
        };

    } catch (error) {
        logger.error(`Error assigning user ${user_id} to groups: ${error.message}`, { user_id, groups });
        return { status: false, error: error.message }; 
    }
}

async function revokeUserFromGroup(user_id, groups) {
    try {
        let revokedGroups = [];
        logger.info('Revoking user from groups.', { user_id, groups });

        let userGroups = await getUserGroups(user_id);
        logger.debug('Fetched current user groups.', { user_id, userGroups });

        // Filter out groups that the user is not part of
        userGroups = userGroups.filter(group => groups.includes(group.id));

        if (!userGroups.length) {
            logger.info('User is not part of the given groups.', { user_id, groups });
            return {
                status: true,
                message: 'The user is not part of the given groups'
            };
        }

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.');

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const groupPromises = [];
        for (const group of userGroups) {
            groupPromises.push(
                axios.delete(
                    `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group.id}/members/${user_id}/$ref`,
                    options
                )
            );
        }

        await Promise.all(groupPromises);
        logger.info(`User ${user_id} has been removed from groups: [${userGroups.map(group => group.id).join(',')}]`, { user_id, groups });

        return {
            status: true,
            message: `User has been removed from groups: [${userGroups.map(group => group.id).join(',')}]`
        };

    } catch (error) {
        logger.error(`Error revoking user ${user_id} from groups: ${error.message}`, { user_id, groups });
        return { status: false, error: error.message };
    }
}


module.exports = {
    getAuthToken,
    getGroupUsers,
    inviteEntraIdUser,
    inviteStatusEntraIdUser,
    checkUserInActiveDirectory,
    assignUserToGroup,
    revokeUserFromGroup
}
