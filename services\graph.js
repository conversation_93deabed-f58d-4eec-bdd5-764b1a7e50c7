const crypto = require('crypto');
const axios = require('axios');
const auth = require('./auth');
const { setUserActivationStatus } = require('./executeQuery.service');
const constants = require('../config/constants.json');
const logger = require('./logger'); // Import the logger
const { encryptEmail } = require('./encryption.service');

async function getAuthToken(){
    let authCacheKey = crypto.createHash('md5').update('authCache').digest("hex");
    logger.info(`Generating auth cache key: ${authCacheKey}`);
    
    let authData = await auth.getToken(auth.tokenRequest);
    let accessToken = authData.accessToken;
    logger.info('Obtained access token');

    return accessToken;
}

/**
 * Gets users under a group
 */
async function getGroupUsers(){
    try {
        const authToken = await getAuthToken();
        if(!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }};
        const userObjectFields = 'id,displayName,mail,createdDateTime,department';
        const groupMatrixInfo = require("./helpers/allGroupMatrix.json");
        let groupUsers = {};

        for (const group of Object.keys(groupMatrixInfo)) {
            try {
                logger.info(`Fetching members for group: ${group}`);
                const response = await axios.get(`${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups/${group}/members?$top=999&$select=${userObjectFields}`, options);
                groupUsers[group] = response.data.value;
                logger.info(`Fetched ${response.data.value.length} members for group ${group}`);
            } catch (exp) {
                if (exp.response && exp.response.status && exp.response.status == 404) {
                    logger.warn(`The group ${group} is not found`);
                } else {
                    logger.error(`Error fetching members for group ${group}: ${exp.message}`);
                }
            }
        }

        groupUsers = Object.entries(groupUsers).map(([group, members]) => {
            return {
                id: group,
                name: groupMatrixInfo[group],
                members: members.map(member => {
                    return {
                        id: member.id,
                        name: member.displayName,
                        email: member.mail,
                        department: member.department,
                        createdDateTime: member.createdDateTime,
                        encEmail: member.mail ? encryptEmail(member.mail) : null
                    };
                })
            };
        });
        
        logger.info('Fetched all group users successfully');
        return groupUsers;
    } catch (error) {
        logger.error(`Failed to get group users: ${error.message}`);
        throw error;
    }
}

// Check if a user exists in Azure Active Directory
async function checkUserInActiveDirectory(userEmail) {
    try {
        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        logger.info(`Checking if user ${userEmail} is part of Active Directory`);
        const response = await axios.get(`${process.env.USER_GRAPH_ENDPOINT}/v1.0/users/${userEmail}`, options);

        if (response.status === 200) {
            logger.info(`User ${userEmail} is part of Active Directory`);
            return response.data;
        }
    } catch (error) {
        if (error.response && error.response.status === 404) {
            logger.info(`User ${userEmail} is not part of Active Directory`);
            return null;  // User does not exist
        }
        logger.error(`Error checking user ${userEmail}: ${error.message}`);
        throw error;
    }
}

/**
 * Invites a user to Microsoft Entra ID 
 */
async function inviteEntraIdUser(user){
    try {
        const authToken = await getAuthToken();
        if(!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }};

        const data = {
          invitedUserEmailAddress: user,
          inviteRedirectUrl: process.env.PLATFORM_BASEPATH,
          sendInvitationMessage: true
        };

        logger.info(`Inviting user: ${user}`);
        const response = await axios.post(`${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.invitations}`, data, options);
        logger.info(`[ENTRAIDINVITER CRON] User ${user} has been invited`);
        const encryptedEmail = encryptEmail(user);
        await setUserActivationStatus(encryptedEmail, 'ENTRA_ID_INVITE_SENT');
        
        logger.info(`User activation status updated for user: ${user}`);
        return response.data;
    } catch (error) {
        logger.error(`Failed to invite user ${user}: ${error.message}`);
        throw error;
    }
}

/**
 * Creates a user directly in Microsoft Entra ID (Azure AD)
 * This creates the user account immediately without requiring invitation acceptance
 * @param {string} email - User's email address
 * @param {string} displayName - User's display name
 * @param {string} password - Temporary password (user will be forced to change on first login)
 * @param {boolean} accountEnabled - Whether the account should be enabled (default: true)
 * @returns {Object} - Created user object with ID
 */
async function createUserInActiveDirectory(email, displayName, password, accountEnabled = true) {
    try {
        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Invalid Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        // Extract mailNickname from email (part before @)
        const mailNickname = email.split('@')[0];
        // Use email as userPrincipalName
        const userPrincipalName = email;

        const data = {
            accountEnabled: accountEnabled,
            displayName: displayName,
            mailNickname: mailNickname,
            userPrincipalName: userPrincipalName,
            passwordProfile: {
                password: password,
                forceChangePasswordNextSignIn: true
            },
            mail: email
        };

        logger.info(`Creating user directly in Azure AD: ${email}`);
        const response = await axios.post(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}`,
            data,
            options
        );
        
        logger.info(`[ENTRAIDCREATE] User ${email} has been created in Azure AD with ID: ${response.data.id}`);
        
        // Update database status to indicate user is created (not invited)
        const encryptedEmail = encryptEmail(email);
        await setUserActivationStatus(encryptedEmail, 'ENTRA_ID_INVITE_SENT');
        
        logger.info(`User activation status updated for user: ${email}`);
        return response.data;
    } catch (error) {
        logger.error(`Failed to create user ${email} in Azure AD: ${error.message}`);
        if (error.response) {
            logger.error(`Error response: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

async function inviteStatusEntraIdUser(user) {
    try {
        logger.info(`Checking invite status for user: ${user}`);

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token');
            throw new Error('Invalid Entra ID Auth Token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$select=Id,userPrincipalName,userType,displayName,mail,accountEnabled,creationType,externalUserState&$filter=mail eq '${user}'`,
            options
        );

        let userData = response.data.value.length ? response.data.value[0] : null;
        if (userData) {
            logger.info(`User data found for ${user}: ${JSON.stringify(userData)}`);
            if (userData.externalUserState === 'Accepted') {
                return {
                    id: userData.id,
                    mail: userData.mail,
                    displayName: userData.displayName,
                    inviteStatus: true,
                    accountEnabled: userData.accountEnabled
                };
            } else {
                return {
                    id: userData.id,
                    mail: userData.mail,
                    displayName: userData.displayName,
                    inviteStatus: false,
                    accountEnabled: userData.accountEnabled
                };
            }
        } else {
            logger.warn(`No user data found for ${user}`);
            throw new Error(`No user data found for ${user}`);
        }
    } catch (error) {
        logger.error(`Error checking invite status for user ${user}: ${error.message}`);
        throw error;
    }
}

async function getUserGroups(user_id) {
    try {
        logger.info('Fetching user groups.', { user_id });

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.', { user_id });

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${user_id}/memberOf`,
            options
        );

        logger.info('User groups fetched successfully.', { user_id, groupCount: response.data.value.length });
        return response.data.value;

    } catch (exp) {
        logger.error(`Error fetching user groups for user ${user_id}: ${exp.message}`, { user_id });
        throw exp;
    }
}


async function assignUserToGroup(user_id, groups) {
    try {
        let assignedGroups = []
        logger.info('Assigning user to groups.', { user_id, groups });

        let userGroups = await getUserGroups(user_id);
        logger.debug('Fetched current user groups.', { user_id, userGroups });

        userGroups.forEach(group => {
            groups = groups.filter(g => g != group.id);
        });

        if (!groups.length) {
            logger.info('User is already assigned to the given groups.', { user_id, groups });
            return {
                status: true,
                message: 'The user is already assigned to the given groups'
            };
        }

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.');

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const groupPromises = [];
        for (const group of groups) {
            const data = {
                "@odata.id": `https://graph.microsoft.com/v1.0/users/${user_id}`
            };
            groupPromises.push(
                axios.post(
                    `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group}/members/$ref`,
                    data,
                    options
                )
            );
        }

        const response = await Promise.all(groupPromises);
        logger.info(`User ${user_id} has been assigned to groups: [${groups.join(',')}]`, { user_id, groups });

        return {
            status: true,
            message: `User has been assigned to [${groups.join(',')}]`
        };

    } catch (error) {
        logger.error(`Error assigning user ${user_id} to groups: ${error.message}`, { user_id, groups });
        return { status: false, error: error.message }; 
    }
}

async function revokeUserFromGroup(user_id, groups) {
    try {
        let revokedGroups = [];
        logger.info('Revoking user from groups.', { user_id, groups });

        let userGroups = await getUserGroups(user_id);
        logger.debug('Fetched current user groups.', { user_id, userGroups });

        // Filter out groups that the user is not part of
        userGroups = userGroups.filter(group => groups.includes(group.id));

        if (!userGroups.length) {
            logger.info('User is not part of the given groups.', { user_id, groups });
            return {
                status: true,
                message: 'The user is not part of the given groups'
            };
        }

        const authToken = await getAuthToken();
        if (!authToken) {
            logger.error('Failed to obtain Entra ID Auth Token.');
            throw new Error('Invalid Entra ID Auth Token');
        }

        logger.debug('Obtained Entra ID Auth Token successfully.');

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const groupPromises = [];
        for (const group of userGroups) {
            groupPromises.push(
                axios.delete(
                    `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group.id}/members/${user_id}/$ref`,
                    options
                )
            );
        }

        await Promise.all(groupPromises);
        logger.info(`User ${user_id} has been removed from groups: [${userGroups.map(group => group.id).join(',')}]`, { user_id, groups });

        return {
            status: true,
            message: `User has been removed from groups: [${userGroups.map(group => group.id).join(',')}]`
        };

    } catch (error) {
        logger.error(`Error revoking user ${user_id} from groups: ${error.message}`, { user_id, groups });
        return { status: false, error: error.message };
    }
}

// Add this function to your services/graph.js file

/**
 * Check if a user exists in Active Directory using various alias formats
 * @param {string} userEmail - The user's email address
 * @returns {Object} - Result object with found status and user data
 */
async function checkUserWithAliases(userEmail) {
    try {
        logger.info(`[GRAPH] Checking user with aliases for: ${userEmail}`);
        
        // Get auth token
        const authToken = await getAuthToken();
        if (!authToken) {
            throw new Error('Failed to obtain Azure AD auth token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        // Generate possible email aliases and formats
        const emailVariations = generateEmailVariations(userEmail);
        logger.info(`[GRAPH] Generated ${emailVariations.length} email variations to check`);
        
        // Try each variation
        for (let i = 0; i < emailVariations.length; i++) {
            const email = emailVariations[i];
            logger.debug(`[GRAPH] Trying variation ${i + 1}/${emailVariations.length}: ${email}`);
            
            try {
                // Method 1: Direct lookup by mail
                let user = await checkUserByMail(email, options);
                if (user) {
                    logger.info(`[GRAPH] User found by mail: ${email} -> ${user.displayName}`);
                    return {
                        found: true,
                        user: user,
                        matchedEmail: email,
                        matchMethod: 'mail'
                    };
                }

                // Method 2: Lookup by userPrincipalName
                user = await checkUserByUPN(email, options);
                if (user) {
                    logger.info(`[GRAPH] User found by UPN: ${email} -> ${user.displayName}`);
                    return {
                        found: true,
                        user: user,
                        matchedEmail: email,
                        matchMethod: 'userPrincipalName'
                    };
                }

                // Method 3: Search by proxyAddresses
                user = await checkUserByProxyAddress(email, options);
                if (user) {
                    logger.info(`[GRAPH] User found by proxy address: ${email} -> ${user.displayName}`);
                    return {
                        found: true,
                        user: user,
                        matchedEmail: email,
                        matchMethod: 'proxyAddresses'
                    };
                }

                // Small delay between API calls to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                logger.debug(`[GRAPH] Error checking ${email}: ${error.message}`);
                continue; // Try next variation
            }
        }

        logger.info(`[GRAPH] User not found with any alias variation for: ${userEmail}`);
        return {
            found: false,
            user: null,
            checkedVariations: emailVariations
        };

    } catch (error) {
        logger.error(`[GRAPH] Error in checkUserWithAliases for ${userEmail}: ${error.message}`);
        return {
            found: false,
            error: error.message,
            userEmail: userEmail
        };
    }
}

/**
 * Generate various email format variations for alias checking
 * @param {string} email - Original email address
 * @returns {Array<string>} - Array of email variations
 */
function generateEmailVariations(email) {
    const variations = new Set();
    
    // Add original email (lowercase)
    variations.add(email.toLowerCase());
    
    // Parse email parts
    const [localPart, domain] = email.toLowerCase().split('@');
    
    if (!localPart || !domain) {
        return [email.toLowerCase()];
    }

    // Common variations for SCAD domain
    if (domain.includes('scad.gov.ae')) {
        // Add variations with different SCAD domains
        variations.add(`${localPart}@scad.gov.ae`);
        variations.add(`${localPart}@scad.ae`);
        variations.add(`${localPart}@adm.scad.gov.ae`);
    }

    // Common name format variations
    if (localPart.includes('.')) {
        const [firstName, lastName] = localPart.split('.');
        
        // firstname.lastname variations
        variations.add(`${firstName}.${lastName}@${domain}`);
        variations.add(`${lastName}.${firstName}@${domain}`);
        
        // Without dot
        variations.add(`${firstName}${lastName}@${domain}`);
        variations.add(`${lastName}${firstName}@${domain}`);
        
        // With underscore
        variations.add(`${firstName}_${lastName}@${domain}`);
        variations.add(`${lastName}_${firstName}@${domain}`);
        
        // First initial + last name
        variations.add(`${firstName.charAt(0)}${lastName}@${domain}`);
        variations.add(`${firstName}${lastName.charAt(0)}@${domain}`);
    }

    // Remove duplicates and return as array
    return Array.from(variations);
}

/**
 * Check user by mail field
 * @param {string} email - Email to check
 * @param {Object} options - HTTP options with auth headers
 * @returns {Object|null} - User object or null
 */
async function checkUserByMail(email, options) {
    try {
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$filter=mail eq '${email}'&$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,createdDateTime`,
            options
        );
        
        return response.data.value && response.data.value.length > 0 ? response.data.value[0] : null;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            return null;
        }
        throw error;
    }
}

/**
 * Check user by userPrincipalName
 * @param {string} email - Email to check as UPN
 * @param {Object} options - HTTP options with auth headers
 * @returns {Object|null} - User object or null
 */
async function checkUserByUPN(email, options) {
    try {
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$filter=userPrincipalName eq '${email}'&$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses`,
            options
        );
        
        return response.data.value && response.data.value.length > 0 ? response.data.value[0] : null;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            return null;
        }
        throw error;
    }
}

/**
 * Check user by proxy addresses (alternate email addresses)
 * @param {string} email - Email to check in proxy addresses
 * @param {Object} options - HTTP options with auth headers
 * @returns {Object|null} - User object or null
 */
async function checkUserByProxyAddress(email, options) {
    try {
        // ProxyAddresses can contain SMTP:email or smtp:email formats
        const smtpEmail = `smtp:${email}`;
        const SMTPEmail = `SMTP:${email}`;
        
        // Try both formats
        for (const proxyFormat of [smtpEmail, SMTPEmail]) {
            try {
                const response = await axios.get(
                    `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$filter=proxyAddresses/any(c:c eq '${proxyFormat}')&$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses`,
                    options
                );
                
                if (response.data.value && response.data.value.length > 0) {
                    return response.data.value[0];
                }
            } catch (error) {
                // Continue to next format
                continue;
            }
        }
        
        return null;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            return null;
        }
        throw error;
    }
}

/**
 * Enhanced user lookup with comprehensive alias checking
 * @param {string} userEmail - The user's email address
 * @returns {Object|null} - User object or null
 */
async function checkUserInActiveDirectoryWithAliases(userEmail) {
    try {
        logger.info(`[GRAPH] Enhanced user lookup for: ${userEmail}`);
        
        // First try direct lookup (fastest)
        let user = await checkUserInActiveDirectory(userEmail);
        if (user) {
            logger.info(`[GRAPH] User found via direct lookup: ${user.displayName}`);
            return user;
        }

        // If direct lookup fails, try alias checking
        logger.info(`[GRAPH] Direct lookup failed, trying alias checking...`);
        const aliasResult = await checkUserWithAliases(userEmail);
        
        if (aliasResult.found) {
            logger.info(`[GRAPH] User found via alias checking: ${aliasResult.user.displayName}`);
            return aliasResult.user;
        }

        logger.info(`[GRAPH] User not found with any method: ${userEmail}`);
        return null;

    } catch (error) {
        logger.error(`[GRAPH] Error in enhanced user lookup for ${userEmail}: ${error.message}`);
        return null;
    }
}

/**
 * Get user ID from email (mail or userPrincipalName)
 * @param {string} userEmail - The user's email address
 * @param {string} authToken - Azure AD access token
 * @returns {string|null} - User ID or null if not found
 */
async function getUserIdByEmail(userEmail, authToken) {
    try {
        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };
        // Search by mail or userPrincipalName
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/users?$filter=mail eq '${userEmail}' or userPrincipalName eq '${userEmail}'&$select=id,displayName,mail,userPrincipalName`,
            options
        );
        if (response.data.value && response.data.value.length > 0) {
            return response.data.value[0].id;
        }
        logger.warn(`No user found with email: ${userEmail}`);
        return null;
    } catch (error) {
        logger.error(`Error fetching user by email ${userEmail}: ${error.message}`);
        return null;
    }
}

/**
 * Get group members by group ID
 * @param {string} groupId - The group ID
 * @returns {Array} - Array of group members
 */
async function getGroupMembers(groupId) {
    try {
        logger.info(`[GRAPH] Fetching members for group: ${groupId}`);
        
        const authToken = await getAuthToken();
        if (!authToken) {
            throw new Error('Failed to obtain Azure AD auth token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups/${groupId}/members?$select=id,displayName,mail,userPrincipalName,accountEnabled,userType,description,groupTypes`,
            options
        );

        logger.info(`[GRAPH] Found ${response.data.value.length} members in group ${groupId}`);
        return response.data.value;

    } catch (error) {
        logger.error(`[GRAPH] Error fetching group members for ${groupId}: ${error.message}`);
        throw error;
    }
}

/**
 * Get group by name (displayName)
 * @param {string} groupName - The group display name
 * @returns {Object|null} - Group object or null if not found
 */
async function getGroupByName(groupName) {
    try {
        logger.info(`[GRAPH] Looking up group by name: ${groupName}`);
        
        const authToken = await getAuthToken();
        if (!authToken) {
            throw new Error('Failed to obtain Azure AD auth token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups?$filter=displayName eq '${groupName}'&$select=id,displayName,description,groupTypes,securityEnabled,mailEnabled`,
            options
        );

        if (response.data.value && response.data.value.length > 0) {
            logger.info(`[GRAPH] Found group: ${groupName} (${response.data.value[0].id})`);
            return response.data.value[0];
        }

        logger.warn(`[GRAPH] Group not found: ${groupName}`);
        return null;

    } catch (error) {
        logger.error(`[GRAPH] Error looking up group by name ${groupName}: ${error.message}`);
        throw error;
    }
}

module.exports = {
    getAuthToken,
    getGroupUsers,
    inviteEntraIdUser,
    inviteStatusEntraIdUser,
    checkUserInActiveDirectory,
    assignUserToGroup,
    revokeUserFromGroup,
    checkUserWithAliases,
    checkUserInActiveDirectoryWithAliases,
    generateEmailVariations,
    getUserIdByEmail,
    getGroupMembers,
    getGroupByName
}
