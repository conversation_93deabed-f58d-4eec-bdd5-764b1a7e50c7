/**
 * Shared utility functions used across multiple jobs
 */

/**
 * Maps email domain to entity ID
 * Extracted from jobs/user-onboarding-sync-existing.job.js
 */
function getEntityIDFromDomain(entityDomain) {
  const domainIdMapping = {
    "addof.gov.ae": "E24",
    "pension.gov.ae": "E16",
    "doe.gov.ae": "E36",
    "ead.gov.ae": "E43",
    "adpolice.gov.ae": "E45",
    "dmt.gov.ae": "E37",
    "dge.gov.ae": "E2",
    "adafsa.gov.ae": "E47",
    "dctabudhabi.ae": "E23",
    "addcd.gov.ae": "E1",
    "ded.abudhabi.ae": "E22",
    "doh.gov.ae": "E4",
    "adsc.gov.ae": "E17",
    "adek.gov.ae": "E3",
    "adcda.gov.ae": "E48",
    "eca.gov.ae": "E15",
    "adha.gov.ae": "E5",
    "adcustoms.gov.ae": "E30",
    "adfca.gov.ae": "E13",
    "ewaa.gov.ae": "E12",
    "maan.gov.ae": "E11",
    "ssa.gov.ae": "E10",
    "adcci.gov.ae": "E26",
    "adeconomy.ae": "E22",
    "ecouncil.ae": "E50",
    "cpc.gov.ae": "E51",
    "adro.gov.ae": "E22",
    "nawah.ae": "E52",
    "scad.gov.ae": "E49",
  };
  return domainIdMapping[entityDomain];
}

/**
 * Initialize database connection
 * Extracted from jobs/user-onboarding-sync-existing.job.js
 */
async function initDatabase() {
  const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
  const logger = require("./logger");
  
  logger.debug("Entering initDatabase function.");
  logger.info("Initializing database module.");
  try {
    await pgSequelize.authenticate();
    logger.info("Database initialized successfully.");
    logger.debug("Exiting initDatabase function successfully.");
  } catch (err) {
    logger.error(`Error during database initialization: ${err.message}`);
    throw err; // Re-throw the error after logging it for proper error handling upstream
  }
}

module.exports = {
  getEntityIDFromDomain,
  initDatabase,
};

