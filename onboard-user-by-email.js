require("dotenv").config();
const { program } = require("commander");

// Import all existing functions from the repository
const {
  createUserData,
  createUserAccessRequest,
  createUserAccess,
  createUserAccessApproval,
  updateUserAccessRequestEntraIdStatus,
  updateUserAccessRequestStatus,
  getUserData,
  setUserActivationStatus,
} = require("./services/executeQuery.service");

const { encryptEmail } = require("./services/encryption.service");
const { inviteEntraIdUser, checkUserInActiveDirectory } = require("./services/graph");
const { getInverseDomainMatrixMap } = require("./services/group.service");
const { getEntityIDFromDomain, initDatabase } = require("./services/utils");
const logger = require("./services/logger");
const { v4: uuidv4 } = require("uuid");

/**
 * Main function to onboard a user by email
 * Uses all existing functions from the repository
 */
async function onboardUserByEmail(options) {
  const {
    email,
    name,
    role = "USER",
    entityId,
    phoneNumber = "NA",
    designation = null,
    activationFlag = "PENDING",
    inviteToAzureAD = false,
    accessPermissions = [],
  } = options;

  try {
    // Initialize database using existing function
    await initDatabase();

    // Validate email
    if (!email || !email.includes("@")) {
      throw new Error("Invalid email address provided");
    }

    const emailLower = email.toLowerCase();
    const encryptedEmail = encryptEmail(emailLower);

    // Check if user already exists using existing getUserData function
    const existingUsers = await getUserData({
      EMAIL: encryptedEmail,
    });
    
    if (existingUsers.length > 0) {
      const existingUser = existingUsers[0];
      logger.warn(`User with email ${email} already exists in database (ID: ${existingUser.ID})`);
      return {
        success: false,
        message: "User already exists",
        userId: existingUser.ID,
      };
    }

    // Extract entity ID from domain using existing function
    const entityDomain = emailLower.split("@")[1].toLowerCase();
    const userEntityId = entityId || getEntityIDFromDomain(entityDomain);
    
    if (!userEntityId) {
      logger.warn(`User: ${emailLower} entity ID not found, skipping sync`);
      throw new Error(
        `Entity ID not found for domain: ${entityDomain}. Please provide --entity-id parameter.`
      );
    }

    // Generate user ID
    const userId = uuidv4();
    
    // Extract name from email if not provided
    const userName = name || emailLower.split("@")[0].replace(/[._-]/g, " ");

    logger.info(`Starting onboarding for user: ${email}`);
    logger.info(`User ID: ${userId}, Entity ID: ${userEntityId}, Role: ${role}`);

    // Step 1: Create user in database using existing createUserData function
    await createUserData(
      userId,
      userName,
      encryptedEmail,
      phoneNumber,
      role,
      userEntityId,
      designation,
      null, // existingUserLinkStatus
      activationFlag,
      0 // ndaStatus
    );
    logger.info(`Created user data for: ${userId}`);

    // Step 2: Create access request (if access permissions provided)
    let requestId = null;
    if (accessPermissions.length > 0) {
      // Use existing createUserAccessRequest function
      requestId = uuidv4();
      await createUserAccessRequest(requestId, userId);
      logger.info(`Created user access request: ${requestId}`);

      // Step 3: Create user access permissions using existing functions
      const domainMatrixmap = getInverseDomainMatrixMap();
      for (const access of accessPermissions) {
        const accessId = uuidv4();
        // Map domain using inverse domain matrix map (existing function)
        const mappedDomain = domainMatrixmap[access.domain] || access.domain;
        
        // Use existing createUserAccess function
        await createUserAccess(
          requestId,
          accessId,
          userId,
          mappedDomain,
          access.classification,
          "GRANT"
        );
        
        // Use existing createUserAccessApproval function
        await createUserAccessApproval(
          uuidv4(),
          accessId,
          access.approvalStatus || "APPROVED",
          null
        );
        logger.info(`Create approved access for domain: ${mappedDomain}, classification: ${access.classification}`);
      }

      // Step 4: Complete the request using existing functions
      await updateUserAccessRequestEntraIdStatus(requestId, "COMPLETED");
      await updateUserAccessRequestStatus(requestId, "COMPLETED");
      logger.info(`Existing user sync complete for ${userId}`);
    }

    // Step 5: Invite to Azure AD if requested using existing functions
    if (inviteToAzureAD) {
      try {
        // Use existing checkUserInActiveDirectory function
        const userStatus = await checkUserInActiveDirectory(emailLower);
        if (userStatus == null) {
          // Use existing inviteEntraIdUser function (this also updates DB status)
          await inviteEntraIdUser(emailLower);
          logger.info(`[ENTRAIDINVITER] User ${emailLower} has been invited`);
        } else {
          // User already exists in Azure AD, update status using existing function
          await setUserActivationStatus(encryptedEmail, "ENTRA_ID_INVITE_SENT");
          logger.info(`User ${emailLower} already exists in Azure AD, status updated`);
        }
      } catch (err) {
        logger.error(`Failed to invite user to Azure AD: ${err.message}`);
        // Don't fail the entire onboarding if Azure AD invite fails
      }
    }

    logger.info(`✓ User onboarding completed successfully for: ${email}`);
    return {
      success: true,
      userId,
      requestId,
      email,
      message: "User onboarded successfully",
    };
  } catch (err) {
    logger.error(`Failed to onboard user ${email}: ${err.message}`);
    logger.error(err.stack);
    throw err;
  }
}

// CLI Setup
program
  .name("onboard-user-by-email")
  .description("Onboard a user to the platform by email address")
  .requiredOption("-e, --email <email>", "User's email address")
  .option("-n, --name <name>", "User's full name (default: extracted from email)")
  .option("-r, --role <role>", "User role (USER, PRIMARY_SUPERUSER, SECONDARY_SUPERUSER, DG)", "USER")
  .option("--entity-id <entityId>", "Entity ID (e.g., E49). Auto-detected from email domain if not provided")
  .option("-p, --phone <phone>", "Phone number", "NA")
  .option("-d, --designation <designation>", "Job designation")
  .option("--activation-flag <flag>", "Activation flag (PENDING, ACTIVE)", "PENDING")
  .option("--invite-azure", "Invite user to Azure AD/Entra ID", false)
  .option("--access <access>", "Access permissions in format: 'domain:classification' (can be used multiple times)")
  .parse(process.argv);

const options = program.opts();

// Parse access permissions if provided
let accessPermissions = [];
if (options.access) {
  const accessArray = Array.isArray(options.access) ? options.access : [options.access];
  accessPermissions = accessArray.map((accessStr) => {
    const [domain, classification, approvalStatus] = accessStr.split(":");
    if (!domain || !classification) {
      throw new Error(
        `Invalid access format: ${accessStr}. Expected format: domain:classification[:approvalStatus]`
      );
    }
    return {
      domain: domain.trim(),
      classification: classification.trim(),
      approvalStatus: approvalStatus ? approvalStatus.trim() : "APPROVED",
    };
  });
}

// Run onboarding
onboardUserByEmail({
  email: options.email,
  name: options.name,
  role: options.role,
  entityId: options.entityId,
  phoneNumber: options.phone,
  designation: options.designation,
  activationFlag: options.activationFlag,
  inviteToAzureAD: options.inviteAzure,
  accessPermissions,
})
  .then((result) => {
    if (result.success) {
      console.log("\n✅ User onboarded successfully!");
      console.log(`   User ID: ${result.userId}`);
      console.log(`   Email: ${result.email}`);
      if (result.requestId) {
        console.log(`   Access Request ID: ${result.requestId}`);
      }
      process.exit(0);
    } else {
      console.log(`\n⚠️  ${result.message}`);
      process.exit(1);
    }
  })
  .catch((err) => {
    console.error(`\n❌ Error: ${err.message}`);
    process.exit(1);
  });
