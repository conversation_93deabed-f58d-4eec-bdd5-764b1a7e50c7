const oracledb = require('oracledb');
const dbConfig = require('../config/dbConfig.js');
const logger = require('../services/logger'); 

async function initialize() {
    try {
        const pool = await oracledb.createPool(dbConfig.hrPool);
        logger.info('Database connection pool created successfully.');
        pool._logStats();  // This logs the pool statistics, useful for debugging
    } catch (err) {
        logger.error(`Error creating database connection pool: ${err.message}`);
        throw err;  // Throw the error after logging it for proper error handling upstream
    }
}

async function close() {
    try {
        await oracledb.getPool().close();
        logger.info('Database connection pool closed successfully.');
    } catch (err) {
        logger.error(`Error closing database connection pool: ${err.message}`);
        throw err;  // Throw the error after logging it for proper error handling upstream
    }
}

function simpleExecute(statement, binds = {}, opts = {}) {
    return new Promise(async (resolve, reject) => {
        let conn;
        logger.debug('Entering simpleExecute method.');

        logger.info(`[Oracle] Executing query: ${statement} with binds: ${JSON.stringify(binds)}`);
        try {
            opts.outFormat = oracledb.OBJECT;
            opts.autoCommit = true;
            conn = await oracledb.getPool().getConnection();

            logger.debug(`Oracle Client Version: ${oracledb.oracleClientVersionString}`);
            logger.debug(`OracleDB npm version: ${oracledb.versionString}`);
            logger.debug(`Oracle Server Version: ${conn.oracleServerVersionString}`);

            const result = await conn.execute(statement, binds, opts);

            logger.debug('Query executed successfully.');
            resolve(result.rows);
        } catch (err) {
            logger.error(`Error executing query: ${err.message}`);
            reject(err);  // Reject with the error for proper error handling
        } finally {
            if (conn) {
                try {
                    logger.debug('Closing database connection.');
                    await conn.close();
                    logger.debug('Database connection closed.');
                } catch (err) {
                    logger.error(`Error closing database connection: ${err.message}`);
                }
            }
            logger.debug('Exiting simpleExecute method.');
        }
    });
}

module.exports = {
    simpleExecute,
    close,
    initialize
};
