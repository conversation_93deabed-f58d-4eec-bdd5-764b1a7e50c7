trigger: none

variables:
  app_name: 'bayaan-schedule-jobs'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'

jobs:
- job: Init
  pool: 'oc-light-agent'
  steps:
  - script: |
        echo "##vso[task.setvariable variable=version;isOutput=true]$(Build.SourceVersion)"
        echo "##vso[build.updatebuildnumber]$(Build.SourceVersion)"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"


- job: DockerBuild
  pool: 'oc-light-agent'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'

  - task: Npm@1
    inputs:
      command: 'install'
      verbose: false
      customRegistry: 'useFeed'
      customFeed: '4ac6f6d3-9411-4182-b105-79854fc1587c'
    displayName: 'npm install'

  - script: |
      tar -czvf node_modules.tar.gz node_modules
    displayName: 'TAR node_modules'

  - script: |
      buildah login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
    failOnStderr: "true"
    displayName: 'login to buildah registry'

  - script: |
      buildah build -t $(registry)/$(app_name):$(version) -f Dockerfile  .  
      buildah push --tls-verify=false $(registry)/$(app_name):$(version)
    displayName: 'Building and pushing image to buildah registry $(registry)/$(app_name):$(version)'


- job: Deploy
  pool: 'oc-light-agent'
  dependsOn: [DockerBuild,Init]
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - script: |
      B64_PAT=$(printf ":%s" "$PAT" | base64)
      git clone -c http.extraheader="AUTHORIZATION: Basic ${B64_PAT}" -b master https://<EMAIL>/tfs/COI-Projects/scad-gitops/_git/scad-gitops  $(Build.SourcesDirectory)/scad-gitops
      cd $(Build.SourcesDirectory)/scad-gitops/bayaangov/envs/staging/jobs
      git config --global user.email "<EMAIL>"
      git config --global user.name "DevOps Server"
      full_image_name="$(OpenShift.Registry.Internal)/azure-devops/$(app_name)"
      yq eval --inplace \
      '(.images[] | select(.name == "'"$full_image_name"'") | .newTag) = "'"$(version)"'"' \
      "kustomization.yaml"
      git add kustomization.yaml
      git commit --allow-empty -m "Release pipeline staging -- $(version)"
      git push
    displayName: 'Change version in gitops repository'
