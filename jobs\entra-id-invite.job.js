require('dotenv').config();

const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
const { decryptEmail, encryptEmail } = require('../services/encryption.service');
const { getUserData, setUserActivationStatus } = require('../services/executeQuery.service');
const { inviteEntraIdUser, checkUserInActiveDirectory } = require('../services/graph');
const logger = require('../services/logger');

async function initDatabase() {
    logger.debug('Entering initDatabase function.');
    logger.info('Initializing database module.');
    try {
        await pgSequelize.authenticate();
        logger.info('Database initialized successfully.');
        logger.debug('Exiting initDatabase function successfully.');
    } catch (err) {
        logger.error(`Error during database initialization: ${err.message}`);
        throw err; // Re-throw the error after logging it for proper error handling upstream
    }
}


async function superuserEntraIdInviter() {
    try {
        logger.info('[ENTRAIDINVITER CRON] Invite Superuser Users start');
        // Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserData({
            ROLE: ['PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER', 'USER', 'DG'],
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG: 'PENDING'
        });

        if (pendingRequests.length) {
            let requestPromises = [];
            for (const request of pendingRequests) {
                // Check for the entra id invite status for request
                if (request.ACTIVATION_FLAG === 'PENDING') {
                    const decryptedEmail = decryptEmail(request.EMAIL);
                    let userStatus = await checkUserInActiveDirectory(decryptedEmail)
                    if (userStatus == null){
                        requestPromises.push(inviteEntraIdUser(decryptedEmail));
                        logger.debug(`Added invite promise for superuser: ${decryptedEmail}`);
                    }
                    else{
                        const encryptedEmail = encryptEmail(decryptedEmail);
                        await setUserActivationStatus(encryptedEmail, 'ENTRA_ID_INVITE_SENT');
                    }
                }
            }
            await Promise.all(requestPromises);
            logger.info('[ENTRAIDINVITER CRON] All Superuser invitations sent successfully');
        } else {
            logger.info('[ENTRAIDINVITER CRON] No Pending Superusers found');
        }
        logger.info('[ENTRAIDINVITER CRON] Invite Superuser Users end');
    } catch (err) {
        logger.error(`Error in superuserEntraIdInviter: ${err.message}`);
        throw err;
    }
}

/**
 * Sends Entra ID invites to SANADKOM approved users
 */
async function dgEntraIdInviter() {
    try {
        logger.info('[DGENTRAIDINVITER CRON] Invite DG Users start');
        // Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserData({
            ROLE: ['DG'],
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG: 'PENDING'
        });

        if (pendingRequests.length) {
            let requestPromises = [];
            for (const request of pendingRequests) {
                // Check for the entra id invite status for request
                if (request.ACTIVATION_FLAG === 'PENDING') {
                    const userEmail = decryptEmail(request.EMAIL);
                    let userStatus = await checkUserInActiveDirectory(userEmail)
                    if (userStatus == null){
                        requestPromises.push(inviteEntraIdUser(userEmail));
                        logger.debug(`Added invite promise for DG user: ${userEmail}`);
                    }
                    else{
                        const encryptedEmail = encryptEmail(userEmail);
                        await setUserActivationStatus(encryptedEmail, 'ENTRA_ID_INVITE_SENT');
                    }
                }
            }
            await Promise.all(requestPromises);
            logger.info('[DGENTRAIDINVITER CRON] All DG user invitations sent successfully');
        } else {
            logger.info('[DGENTRAIDINVITER CRON] No DG Users found');
        }
        logger.info('[DGENTRAIDINVITER CRON] Invite DG Users end');
    } catch (err) {
        logger.error(`Error in dgEntraIdInviter: ${err.message}`);
        throw err;
    }
}

/**
 * Sends Entra ID invites to SANADKOM approved users
 */
async function userEntraIdInviter() {
    try {
        logger.info('[USERENTRAIDINVITER CRON] Invite Users start');
        // Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserData({
            ROLE: ['USER'],
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG: 'PENDING'
        });

        if (pendingRequests.length) {
            let requestPromises = [];
            for (const request of pendingRequests) {
                const decryptedEmail = decryptEmail(request.EMAIL);
                let userStatus = await checkUserInActiveDirectory(decryptedEmail)
                if (userStatus == null){
                    requestPromises.push(inviteEntraIdUser(decryptedEmail));
                    logger.debug(`Added invite promise for user: ${decryptedEmail}`);
                }
                else{
                    const encryptedEmail = encryptEmail(decryptedEmail);
                    await setUserActivationStatus(encryptedEmail, 'ACTIVE');
                }
            }
            await Promise.all(requestPromises);
            logger.info('[USERENTRAIDINVITER CRON] All user invitations sent successfully');
        } else {
            logger.info('[USERENTRAIDINVITER CRON] No Users found');
        }
        logger.info('[USERENTRAIDINVITER CRON] Invite Users end');
    } catch (err) {
        logger.error(`Error in userEntraIdInviter: ${err.message}`);
        throw err;
    }
}

const run = async () => {
    await initDatabase();
  
    try {
        await superuserEntraIdInviter();
        logger.info("Superuser Inviter completed.");
    } catch (err) {
        logger.error(`Failed to invite superusers: ${err.message}`);
    }
  
    try {
        await dgEntraIdInviter();
        logger.info("DG Inviter completed.");
    } catch (err) {
        logger.error(`Failed to invite DGs: ${err.message}`);
    }

    try {
        await userEntraIdInviter();
        logger.info("User Inviter completed.");
    } catch (err) {
        logger.error(`Failed to invite Users: ${err.message}`);
    }
};

module.exports = {
    run
};