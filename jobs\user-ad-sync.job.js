/**
 * Job that pulls SCAD users from SCAD Acitve Directory to Clickhouse table.
 * 
 * Author: <EMAIL>
 */

require('dotenv').config();

const database = require('../services/database.service');
const { getGroupUsers } = require('../services/graph');
const { updateADUserList, truncateADUserList } = require('../services/executeQuery.service');
const { assignDrillDownGroup } = require('../services/helpers/functions')

async function initDatabase() {
    console.debug(`>>>>> Enter server.initDatabase`);
    console.info(`Initializing database module`);
    try {
        await database.initialize();
        console.info(`Initialised database`);
        console.debug(`<<<<< Exit server.initDatabase successfully`);
    } catch (err) {
        console.error(`<<<<< Exit server.initDatabase with error ${err}`);
    }
}

async function run() {
    await initDatabase();
    let groupsData = await getGroupUsers();
    groupsData = assignDrillDownGroup(groupsData);
    await truncateADUserList();

    if (groupsData?.length) {
        await Promise.all(groupsData.map(element => updateADUserList([element])));
    }

    process.exit(0);
}


module.exports = { run };