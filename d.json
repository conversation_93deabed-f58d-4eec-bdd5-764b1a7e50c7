{"language": "EN", "indicatorTools": [{"id": "export-png", "disabled": true, "label": "Export PNG"}, {"id": "export-csv", "label": "Export CSV"}], "multiDrivers": true, "indicatorDrivers": [{"title": "Oil Price", "type": "radio", "id": "parameter_1_range", "subtitle": "Range", "options": [{"label": "-50%", "value": "very low", "isSelected": false}, {"label": "-25%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "25%", "value": "high", "isSelected": false}, {"label": "50%", "value": "very high", "isSelected": false}]}, {"title": "Singapore GDP", "type": "radio", "id": "parameter_2_range", "subtitle": "Range", "options": [{"label": "-10%", "value": "very low", "isSelected": false}, {"label": "-5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "5%", "value": "high", "isSelected": false}, {"label": "10%", "value": "very high", "isSelected": false}]}, {"title": "PMI", "id": "parameter_3_range", "type": "radio", "subtitle": "Range", "note": "", "options": [{"label": "-40%", "value": "very low", "isSelected": false}, {"label": "-20%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "20%", "value": "high", "isSelected": false}, {"label": "40%", "value": "very high", "isSelected": false}]}, {"title": "US Dollar Price", "type": "radio", "id": "parameter_4_range", "subtitle": "Range", "options": [{"label": "-5%", "value": "very low", "isSelected": false}, {"label": "-2.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "2.5%", "value": "high", "isSelected": false}, {"label": "5%", "value": "very high", "isSelected": false}]}, {"title": "Government Spending", "type": "radio", "id": "parameter_5_range", "subtitle": "Range", "options": [{"label": "-5%", "value": "very low", "isSelected": false}, {"label": "-2.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "2.5%", "value": "high", "isSelected": false}, {"label": "5%", "value": "very high", "isSelected": false}]}, {"title": "Geo Political Index", "type": "radio", "id": "parameter_6_range", "subtitle": "Range", "options": [{"label": "-5%", "value": "very low", "isSelected": false}, {"label": "-2.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "2.5%", "value": "high", "isSelected": false}, {"label": "5%", "value": "very high", "isSelected": false}]}, {"title": "TAC Index Air freight W", "type": "radio", "id": "parameter_7_range", "subtitle": "Range", "options": [{"label": "-50%", "value": "very low", "isSelected": false}, {"label": "-25%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "25%", "value": "high", "isSelected": false}, {"label": "50%", "value": "very high", "isSelected": false}]}, {"title": "Exchange Rate (AED/INR)", "type": "radio", "id": "parameter_8_range", "subtitle": "Range", "options": [{"label": "-10%", "value": "very low", "isSelected": false}, {"label": "-5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "5%", "value": "high", "isSelected": false}, {"label": "10%", "value": "very high", "isSelected": false}]}], "indicatorValues": {"multiValuesMeta": [[{"id": "current-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "number_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "estimate", "title": "% change {dateStart} to {dateEnd}", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "quarter-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "number_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "last-nowcast-first-forecast", "title": "% change {dateStart} to {dateEnd} ", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.1-1", "templateFormat": "yyyy", "invertColor": true}], [{"id": "current-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "percentage_1.2-2", "templateFormat": "yyyy", "isPercentageValue": true, "invertColor": true}, {"id": "estimate", "title": "% change {dateStart} to {dateEnd}", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.2-2", "templateFormat": "yyyy", "isPercentageValue": true, "invertColor": true}, {"id": "quarter-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "percentage_1.2-2", "templateFormat": "yyyy", "isPercentageValue": true, "invertColor": true}, {"id": "last-nowcast-first-forecast", "title": "% change {dateStart} to {dateEnd} ", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.2-2", "templateFormat": "yyyy", "isPercentageValue": true, "invertColor": true}]], "overviewValuesMeta": []}, "indicatorVisualizations": {"visualizationsMeta": [{"id": "line-chart-economy-cpi-constant", "isInflation": true, "vizLabel": "CPI Label", "sortOrder": 1, "type": "line-chart", "invertColor": true, "comboIdTable": "VW_COMBINATION_DF_NEW_SCHEMA", "viewName": "VW_INFLATION_SCENARIO", "filterBy": {"INDICATOR_ID": "CPI_EMIRATE_OF_ABU_DHABI"}, "seriesMeta": [{"id": "economic-index", "label": "Consumer price index", "color": "#3667ff", "type": "solid", "dimension": {"TYPE": "NOWCAST", "INDICATOR_ID": "CPI_EMIRATE_OF_ABU_DHABI"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "economic-index-forecast", "label": "Consumer price index", "color": "#3667ff", "type": "forecast-with-arrow", "dimension": {"TYPE": "FORECAST", "INDICATOR_ID": "CPI_EMIRATE_OF_ABU_DHABI"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}], "markersMeta": [{"id": "economic-index_real-vs-forecast", "color": "#ffffff", "type": "line-with-label", "labelText": "Forecast", "axis": "x", "accessor": {"type": "date", "path": "DATE", "specifier": "%Y-%m-%d"}}], "showInterval": true, "showQuarterlyIntervals": false, "showPointLabels": true, "xAxisLabel": null, "timeUnit": ["Yearly"], "yAxisLabel": "Index", "yAxisExtraStepMin": 0.005, "yAxisExtraStepMax": 0.005, "xAxisFormat": "date_y", "yAxisFormat": "d3-number", "tooltipTitleFormat": "date_y", "tooltipValueFormat": "number_1.1-1"}, {"id": "line-chart-economy-inflation-constant", "isInflation": true, "isPercentageValue": true, "invertColor": true, "vizLabel": "Inflation Label", "sortOrder": 2, "type": "line-chart", "comboIdTable": "VW_COMBINATION_DF_NEW_SCHEMA", "viewName": "VW_INFLATION_SCENARIO", "filterBy": {"INDICATOR_ID": "INFLATION_EMIRATE_OF_ABU_DHABI"}, "seriesMeta": [{"id": "inflation-rate", "label": "Inflation Rate", "color": "#3667ff", "type": "solid", "dimension": {"TYPE": "NOWCAST", "INDICATOR_ID": "INFLATION_EMIRATE_OF_ABU_DHABI"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "inflation-rate-forecast", "label": "Inflation Rate", "color": "#3667ff", "type": "forecast-with-arrow", "dimension": {"TYPE": "FORECAST", "INDICATOR_ID": "INFLATION_EMIRATE_OF_ABU_DHABI"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}], "markersMeta": [{"id": "inflation_rate_real-vs-forecast", "color": "#ffffff", "type": "line-with-label", "labelText": "Forecast", "axis": "x", "accessor": {"type": "date", "path": "DATE", "specifier": "%Y-%m-%d"}}], "showInterval": true, "showQuarterlyIntervals": false, "showPointLabels": true, "xAxisLabel": null, "yAxisLabel": "Rate", "timeUnit": ["Yearly"], "yAxisExtraStepMin": 0.005, "yAxisExtraStepMax": 0.005, "xAxisFormat": "date_y", "yAxisFormat": "d3-number", "tooltipTitleFormat": "date_y", "tooltipValueFormat": "number_1.1-1"}], "visualizationDefault": "line-chart-economy-inflation-constant"}, "indicatorType": "analytical-apps", "unit": "%"}