require('dotenv').config();
const { getAuthToken, checkUserInActiveDirectory, checkUserWithAliases } = require('./services/graph');
const axios = require('axios');
const logger = require('./services/logger');
const constants = require('./config/constants.json');

/**
 * Check all groups for a user by email
 * @param {string} userEmail - The user's email address
 * @returns {Object} - User groups information
 */
async function checkUserGroups(userEmail) {
    try {
        console.log(`\n🔍 Checking groups for user: ${userEmail}`);
        console.log('='.repeat(60));
        
        // Initialize database connection
        const database = require('./services/database.service');
        await database.initialize();
        
        // Get auth token
        const authToken = await getAuthToken();
        if (!authToken) {
            throw new Error('Failed to obtain Azure AD auth token');
        }

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        // Step 1: Find the user (try direct lookup first, then aliases)
        console.log('📋 Step 1: Looking up user in Azure AD...');
        let user = await checkUserInActiveDirectory(userEmail.toLowerCase());
        
        if (!user) {
            console.log('   Direct lookup failed. Trying alias checking...');
            const aliasResult = await checkUserWithAliases(userEmail);
            if (aliasResult && aliasResult.found) {
                user = aliasResult.user;
                console.log(`   Found user via alias: ${user.displayName}`);
            }
        } else {
            console.log(`   Found user directly: ${user.displayName}`);
        }
        
        if (!user) {
            return {
                success: false,
                userFound: false,
                userEmail: userEmail,
                message: 'User not found in Azure AD'
            };
        }

        console.log(`\n👤 User Information:`);
        console.log(`   Name: ${user.displayName}`);
        console.log(`   Email: ${user.mail || user.userPrincipalName}`);
        console.log(`   User ID: ${user.id}`);
        console.log(`   Created At: ${user.createdDateTime}`);
        console.log(`   Account Enabled: ${user.accountEnabled !== false}`);

        // Step 2: Get user's direct group memberships
        console.log('\n📋 Step 2: Fetching direct group memberships...');
        const directGroupsResponse = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${user.id}/memberOf?$select=id,displayName,description,groupTypes,securityEnabled,mailEnabled`,
            options
        );

        const directGroups = directGroupsResponse.data.value;
        console.log(`   Found ${directGroups.length} direct group memberships`);

        // Step 3: Get transitive group memberships (includes nested groups)
        console.log('\n📋 Step 3: Fetching transitive group memberships...');
        const transitiveGroupsResponse = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${user.id}/transitiveMemberOf?$select=id,displayName,description,groupTypes,securityEnabled,mailEnabled`,
            options
        );

        const transitiveGroups = transitiveGroupsResponse.data.value;
        console.log(`   Found ${transitiveGroups.length} total group memberships (including nested)`);

        // Step 4: Categorize groups
        const categorizedGroups = {
            security: [],
            distribution: [],
            microsoft365: [],
            matrix: [],
            bayaan: [],
            other: []
        };

        directGroups.forEach(group => {
            const groupName = group.displayName || ""
            
            if (groupName.includes('matrix')) {
                categorizedGroups.matrix.push(group);
            } else if (groupName.includes('bayaan')) {
                categorizedGroups.bayaan.push(group);
            } else if (group.groupTypes && group.groupTypes.includes('Unified')) {
                categorizedGroups.microsoft365.push(group);
            } else if (group.securityEnabled && !group.mailEnabled) {
                categorizedGroups.security.push(group);
            } else if (!group.securityEnabled && group.mailEnabled) {
                categorizedGroups.distribution.push(group);
            } else {
                categorizedGroups.other.push(group);
            }
        });

        // Step 5: Display results
        console.log('\n📊 Group Categorization:');
        console.log(`   Security Groups: ${categorizedGroups.security.length}`);
        console.log(`   Distribution Groups: ${categorizedGroups.distribution.length}`);
        console.log(`   Microsoft 365 Groups: ${categorizedGroups.microsoft365.length}`);
        console.log(`   MATRIX Groups: ${categorizedGroups.matrix.length}`);
        console.log(`   BAYAAN Groups: ${categorizedGroups.bayaan.length}`);
        console.log(`   Other Groups: ${categorizedGroups.other.length}`);

        // Display MATRIX groups (most important)
        if (categorizedGroups.matrix.length > 0) {
            console.log('\n🔐 MATRIX Groups:');
            categorizedGroups.matrix.forEach(group => {
                console.log(`   • ${group.displayName}  - ID: ${group.id}`);
                if (group.description) {
                    console.log(`     Description: ${group.description}`);
                }
            });
        }

        // Display BAYAAN groups
        if (categorizedGroups.bayaan.length > 0) {
            console.log('\n🏢 BAYAAN Groups:');
            categorizedGroups.bayaan.forEach(group => {
                console.log(`   • ${group.displayName} - ID: ${group.id}`);
                if (group.description) {
                    console.log(`     Description: ${group.description}`);
                }
            });
        }

        // Display security groups
        if (categorizedGroups.security.length > 0) {
            console.log('\n🛡️ Security Groups:');
            categorizedGroups.security.forEach(group => {
                console.log(`   • ${group.displayName} - ID: ${group.id}`);
            });
        }

        const result = {
            success: true,
            userFound: true,
            user: {
                id: user.id,
                displayName: user.displayName,
                email: user.mail || user.userPrincipalName,
                userPrincipalName: user.userPrincipalName,
                accountEnabled: user.accountEnabled !== false
            },
            groups: {
                direct: directGroups,
                transitive: transitiveGroups,
                categorized: categorizedGroups
            },
            summary: {
                totalDirectGroups: directGroups.length,
                totalTransitiveGroups: transitiveGroups.length,
                matrixGroups: categorizedGroups.matrix.length,
                bayaanGroups: categorizedGroups.bayaan.length,
                securityGroups: categorizedGroups.security.length
            }
        };

        console.log('\n✅ Group check completed successfully!');
        console.log('='.repeat(60));
        
        return result;

    } catch (error) {
        console.error(`\n❌ Error checking user groups: ${error.message}`);
        return {
            success: false,
            error: error.message,
            userEmail: userEmail
        };
    }
}

/**
 * Export groups to CSV for analysis
 * @param {Object} result - Result from checkUserGroups
 * @param {string} outputPath - Output file path
 */
async function exportGroupsToCSV(result, outputPath = './user-groups-export.csv') {
    try {
        const fs = require('fs');
        const path = require('path');
        
        if (!result.success || !result.userFound) {
            console.log('No data to export');
            return;
        }

        let csvContent = 'Group Name,Group ID,Description,Type,Security Enabled,Mail Enabled\n';
        
        result.groups.direct.forEach(group => {
            const type = group.groupTypes && group.groupTypes.includes('Unified') ? 'Microsoft 365' :
                        group.securityEnabled && !group.mailEnabled ? 'Security' :
                        !group.securityEnabled && group.mailEnabled ? 'Distribution' : 'Other';
            
            csvContent += `"${group.displayName}","${group.id}","${group.description || ''}","${type}","${group.securityEnabled}","${group.mailEnabled}"\n`;
        });

        fs.writeFileSync(outputPath, csvContent);
        console.log(`\n📄 Groups exported to: ${path.resolve(outputPath)}`);
        
    } catch (error) {
        console.error(`Error exporting to CSV: ${error.message}`);
    }
}

/**
 * Check multiple users at once
 * @param {Array<string>} userEmails - Array of user emails
 */
async function checkMultipleUsers(userEmails) {
    console.log(`\n🔍 Checking groups for ${userEmails.length} users...\n`);
    
    const results = [];
    
    for (let i = 0; i < userEmails.length; i++) {
        const email = userEmails[i];
        console.log(`[${i + 1}/${userEmails.length}] Processing: ${email}`);
        
        const result = await checkUserGroups(email);
        results.push(result);
        
        // Add delay between requests to avoid rate limiting
        if (i < userEmails.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // Summary
    console.log('\n📊 Batch Processing Summary:');
    const successful = results.filter(r => r.success && r.userFound).length;
    const notFound = results.filter(r => r.success && !r.userFound).length;
    const errors = results.filter(r => !r.success).length;
    
    console.log(`   Successfully processed: ${successful}`);
    console.log(`   Users not found: ${notFound}`);
    console.log(`   Errors: ${errors}`);
    
    return results;
}

// CLI Usage
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('\n📖 Usage Examples:');
        console.log('node check-user-groups.js <EMAIL>');
        console.log('node check-user-groups.js <EMAIL> <EMAIL> <EMAIL>');
        console.log('node check-user-groups.js --export <EMAIL>');
        console.log('\n📝 Options:');
        console.log('--export : Export groups to CSV file');
        process.exit(1);
    }

    try {
        const exportMode = args.includes('--export');
        const emails = args.filter(arg => !arg.startsWith('--'));
        
        if (emails.length === 1) {
            // Single user
            const result = await checkUserGroups(emails[0]);
            
            if (exportMode && result.success) {
                await exportGroupsToCSV(result, `./groups-${emails[0].replace('@', '-')}.csv`);
            }
        } else {
            // Multiple users
            await checkMultipleUsers(emails);
        }
        
    } catch (error) {
        console.error('❌ Script Error:', error.message);
        process.exit(1);
    }
}

// Export functions for use in other scripts
module.exports = {
    checkUserGroups,
    exportGroupsToCSV,
    checkMultipleUsers
};

// Run if called directly
if (require.main === module) {
    main();
}