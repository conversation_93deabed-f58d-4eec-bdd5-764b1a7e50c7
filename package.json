{"name": "user-info-cron", "version": "1.0.0", "description": "", "main": "run.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.11.1", "@clickhouse/client": "^1.4.1", "axios": "^1.7.2", "commander": "^12.1.0", "dotenv": "^16.4.5", "ioredis": "^5.4.1", "moment": "^2.30.1", "nodemailer": "^6.9.15", "nunjucks": "^3.2.4", "oracledb": "^6.5.1", "pg": "^8.16.3", "qrcode": "^1.5.4", "redis": "^4.7.0", "sequelize": "^6.37.6", "winston": "^3.14.2"}}