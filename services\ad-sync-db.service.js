/**
 * Database service for AD Sync operations
 * Handles connections and queries for AD_SYNC_USERS and AD_SYNC_USERS_GROUPS tables
 */

const { Sequelize } = require('sequelize');
const dbConfig = require('../config/adSyncDbConfig');
const logger = require('./logger');

const { literal } = Sequelize;

class ADSyncDatabaseService {
    constructor() {
        this.sequelize = new Sequelize(
            dbConfig.database,
            dbConfig.username,
            dbConfig.password,
            {
                host: dbConfig.host,
                port: dbConfig.port,
                dialect: dbConfig.dialect,
                logging: dbConfig.logging ? (msg) => logger.debug(msg) : false,
                pool: dbConfig.pool,
                dialectOptions: dbConfig.dialectOptions
            }
        );
    }

    /**
     * Initialize database connection
     */
    async initialize() {
        try {
            await this.sequelize.authenticate();
            logger.info('AD Sync database connection established successfully');
        } catch (error) {
            logger.error(`Unable to connect to AD Sync database: ${error.message}`);
            throw error;
        }
    }

    /**
     * Close database connection
     */
    async close() {
        try {
            await this.sequelize.close();
            logger.info('AD Sync database connection closed');
        } catch (error) {
            logger.error(`Error closing AD Sync database connection: ${error.message}`);
            throw error;
        }
    }

    /**
     * Execute a raw query
     */
    async executeQuery(query, binds = {}) {
        try {
            const [results] = await this.sequelize.query(query, {
                replacements: binds,
                type: Sequelize.QueryTypes.RAW
            });
            return results;
        } catch (error) {
            logger.error(`Error executing query: ${error.message}`);
            logger.error(`Query: ${query}`);
            logger.error(`Binds: ${JSON.stringify(binds)}`);
            throw error;
        }
    }

    /**
     * Truncate AD_SYNC_USERS table
     */
    async truncateUsersTable() {
        try {
            const query = 'TRUNCATE TABLE "AD_SYNC_USERS"';
            await this.executeQuery(query);
            logger.info('Successfully truncated AD_SYNC_USERS table');
        } catch (error) {
            logger.error(`Error truncating AD_SYNC_USERS table: ${error.message}`);
            throw error;
        }
    }

    /**
     * Truncate AD_SYNC_USERS_GROUPS table
     */
    async truncateUserGroupsTable() {
        try {
            const query = 'TRUNCATE TABLE "AD_SYNC_USERS_GROUPS"';
            await this.executeQuery(query);
            logger.info('Successfully truncated AD_SYNC_USERS_GROUPS table');
        } catch (error) {
            logger.error(`Error truncating AD_SYNC_USERS_GROUPS table: ${error.message}`);
            throw error;
        }
    }

    /**
     * Insert users into AD_SYNC_USERS table using batch insert
     */
    async insertUsers(users) {
        if (!users || users.length === 0) {
            logger.warn('No users to insert');
            return;
        }

        try {
            const batchSize = 500; // Insert in batches for better performance
            const totalBatches = Math.ceil(users.length / batchSize);

            for (let i = 0; i < totalBatches; i++) {
                const batch = users.slice(i * batchSize, (i + 1) * batchSize);
                await this.insertUsersBatch(batch);
                logger.info(`Inserted batch ${i + 1}/${totalBatches} (${batch.length} users)`);
            }

            logger.info(`Successfully inserted ${users.length} users into AD_SYNC_USERS`);
        } catch (error) {
            logger.error(`Error inserting users: ${error.message}`);
            throw error;
        }
    }

    /**
     * Insert a batch of users
     */
    async insertUsersBatch(users) {
        const columns = [
            '"ID"', '"NAME"', '"EMAIL"', '"ACCOUNT_ENABLED"', '"USER_TYPE"',
            '"DEPARTMENT"', '"JOB_TITLE"', '"CREATED_DATE_TIME"', '"ALIASES"'
        ];

        const binds = {};
        const values = [];
        let bindCount = 0;

        users.forEach((user) => {
            const valuePlaceholders = [];
            
            // ID (UUID)
            const idKey = `id${bindCount}`;
            binds[idKey] = user.id;
            valuePlaceholders.push(`:${idKey}`);
            bindCount++;

            // NAME
            const nameKey = `name${bindCount}`;
            binds[nameKey] = user.name || null;
            valuePlaceholders.push(`:${nameKey}`);
            bindCount++;

            // EMAIL
            const emailKey = `email${bindCount}`;
            binds[emailKey] = user.email || null;
            valuePlaceholders.push(`:${emailKey}`);
            bindCount++;

            // ACCOUNT_ENABLED
            const enabledKey = `enabled${bindCount}`;
            binds[enabledKey] = user.accountEnabled !== false;
            valuePlaceholders.push(`:${enabledKey}`);
            bindCount++;

            // USER_TYPE
            const userTypeKey = `userType${bindCount}`;
            binds[userTypeKey] = user.userType || null;
            valuePlaceholders.push(`:${userTypeKey}`);
            bindCount++;

            // DEPARTMENT
            const deptKey = `dept${bindCount}`;
            binds[deptKey] = user.department || null;
            valuePlaceholders.push(`:${deptKey}`);
            bindCount++;

            // JOB_TITLE
            const jobTitleKey = `jobTitle${bindCount}`;
            binds[jobTitleKey] = user.jobTitle || null;
            valuePlaceholders.push(`:${jobTitleKey}`);
            bindCount++;

            // CREATED_DATE_TIME
            const createdKey = `created${bindCount}`;
            binds[createdKey] = user.createdDateTime ? new Date(user.createdDateTime) : null;
            valuePlaceholders.push(`:${createdKey}`);
            bindCount++;

            // ALIASES (JSONB) - PostgreSQL automatically converts JSON strings to JSONB
            const aliasesKey = `aliases${bindCount}`;
            binds[aliasesKey] = JSON.stringify(user.aliases || []);
            valuePlaceholders.push(`:${aliasesKey}`);
            bindCount++;

            values.push(`(${valuePlaceholders.join(', ')})`);
        });

        const query = `
            INSERT INTO "AD_SYNC_USERS" (${columns.join(', ')})
            VALUES ${values.join(', ')}
        `;

        await this.executeQuery(query, binds);
    }

    /**
     * Insert user-group relationships into AD_SYNC_USERS_GROUPS table
     */
    async insertUserGroups(userGroupRelations) {
        if (!userGroupRelations || userGroupRelations.length === 0) {
            logger.warn('No user-group relations to insert');
            return;
        }

        try {
            const batchSize = 1000; // Insert in batches for better performance
            const totalBatches = Math.ceil(userGroupRelations.length / batchSize);

            for (let i = 0; i < totalBatches; i++) {
                const batch = userGroupRelations.slice(i * batchSize, (i + 1) * batchSize);
                await this.insertUserGroupsBatch(batch);
                logger.info(`Inserted batch ${i + 1}/${totalBatches} (${batch.length} relations)`);
            }

            logger.info(`Successfully inserted ${userGroupRelations.length} user-group relations`);
        } catch (error) {
            logger.error(`Error inserting user-group relations: ${error.message}`);
            throw error;
        }
    }

    /**
     * Insert a batch of user-group relations
     */
    async insertUserGroupsBatch(relations) {
        const binds = {};
        const values = [];
        let bindCount = 0;

        relations.forEach((relation) => {
            const valuePlaceholders = [];
            
            // USER_ID
            const userIdKey = `userId${bindCount}`;
            binds[userIdKey] = relation.userId;
            valuePlaceholders.push(`:${userIdKey}`);
            bindCount++;

            // GROUP_ID
            const groupIdKey = `groupId${bindCount}`;
            binds[groupIdKey] = relation.groupId;
            valuePlaceholders.push(`:${groupIdKey}`);
            bindCount++;

            values.push(`(${valuePlaceholders.join(', ')})`);
        });

        const query = `
            INSERT INTO "AD_SYNC_USERS_GROUPS" ("USER_ID", "GROUP_ID")
            VALUES ${values.join(', ')}
            ON CONFLICT ("USER_ID", "GROUP_ID") DO NOTHING
        `;

        await this.executeQuery(query, binds);
    }
}

module.exports = { ADSyncDatabaseService };

