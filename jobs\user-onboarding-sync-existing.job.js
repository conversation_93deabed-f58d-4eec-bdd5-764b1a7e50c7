require("dotenv").config();
const {
  getExistingUsers,
  createUserData,
  createUserAccessRequest,
  createUserAccess,
  createUserRole,
  createUserAccessApproval,
  updateUserAccessRequestEntraIdStatus,
  updateUserAccessRequestStatus,
} = require("../services/executeQuery.service");
const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
const logger = require("../services/logger");
const { encryptEmail } = require("../services/encryption.service");
const { v4: uuidv4 } = require("uuid");
const { getInverseDomainMatrixMap } = require("../services/group.service");

async function initDatabase() {
  logger.debug("Entering initDatabase function.");
  logger.info("Initializing database module.");
  try {
    await pgSequelize.authenticate();
    logger.info("Database initialized successfully.");
    logger.debug("Exiting initDatabase function successfully.");
  } catch (err) {
    logger.error(`Error during database initialization: ${err.message}`);
    throw err; // Re-throw the error after logging it for proper error handling upstream
  }
}

function getEntityIDFromDomain(entityDomain) {
  const domainIdMapping = {
    "addof.gov.ae": "E24",
    "pension.gov.ae": "E16",
    "doe.gov.ae": "E36",
    "ead.gov.ae": "E43",
    "adpolice.gov.ae": "E45",
    "dmt.gov.ae": "E37",
    "dge.gov.ae": "E2",
    "adafsa.gov.ae": "E47",
    "dctabudhabi.ae": "E23",
    "addcd.gov.ae": "E1",
    "ded.abudhabi.ae": "E22",
    "doh.gov.ae": "E4",
    "adsc.gov.ae": "E17",
    "adek.gov.ae": "E3",
    "adcda.gov.ae": "E48",
    "eca.gov.ae": "E15",
    "adha.gov.ae": "E5",
    "adcustoms.gov.ae": "E30",
    "adfca.gov.ae": "E13",
    "ewaa.gov.ae": "E12",
    "maan.gov.ae": "E11",
    "ssa.gov.ae": "E10",
    "adcci.gov.ae": "E26",
    "adeconomy.ae": "E22",
    "ecouncil.ae": "E50",
    "cpc.gov.ae": "E51",
    "adro.gov.ae": "E22",
    "nawah.ae": "E52",
    "scad.gov.ae": "E49",
  }
  return domainIdMapping[entityDomain]
}

const run = async () => {
  await initDatabase();

  try {
    const result = await getExistingUsers();
    const domainMatrixmap = getInverseDomainMatrixMap();

    for (const user of result) {
      const userId = user.ID;
      const userName = user.NAME;
      let matrixGroups = user.AD_GROUPS.split(", ").filter((g) =>
        g.includes("MATRIX")
      );
      const encryptedEmail = encryptEmail(user.EMAIL.toLowerCase());

      if (!matrixGroups.length > 0) {
        logger.warn(
          `User: ${userId} does not have any IFP MATRIX Groups, skipping sync`
        );
        continue;
      }

      const entityDomain = user.EMAIL.split("@")[1].toLowerCase()
      const userEntityId = getEntityIDFromDomain(entityDomain)
      if (!userEntityId) {
        logger.warn(`User: ${userId} entity ID not found, skipping sync`);
        continue;
      }

      // Insert User
      await createUserData(
        userId,
        userName,
        encryptedEmail,
        "NA",
        'USER',
        userEntityId,
        null,
        "EXISTING_UNLINKED",
        'ACTIVE',
        0
      );
      logger.info(`Created user data for: ${userId}`);

      // Create Access Request
      const requestId = uuidv4();
      await createUserAccessRequest(requestId, userId);
      logger.info(`Created user access request: ${requestId}`);

      // Insert User Access
      for (const group of matrixGroups) {
        const accessId = uuidv4();
        let [_, domain, classification] = group.split("__");
        domain = domainMatrixmap[domain];
        await createUserAccess(
          requestId,
          accessId,
          userId,
          domain,
          classification,
          "GRANT"
        );
        await createUserAccessApproval(uuidv4(), accessId, "APPROVED", null);
        logger.info(`Create approved access for group: ${group}`);
      }

      // Insert User Access Request set to STATUS = COMPLETE AND ENTRA_ID_STATUS = COMPLETE
      await updateUserAccessRequestEntraIdStatus(requestId, "COMPLETED");
      await updateUserAccessRequestStatus(requestId, "COMPLETED");
      logger.info(`Existing user sync complete for ${userId}`);

      await createUserRole(userId);

    }
  } catch (err) {
    logger.error(`Failed to sync existing mobile: ${err.message}`);
  }
};

module.exports = {
  run,
};
