/**
 * Job to sync users from Azure AD by groups to PostgreSQL tables
 * 
 * This job:
 * 1. Fetches users from Azure AD by groups
 * 2. Truncates AD_SYNC_USERS table and inserts fetched users
 * 3. Truncates AD_SYNC_USERS_GROUPS table and inserts user-group relationships
 */

require('dotenv').config();

const { ADUserFetcherService } = require('../services/ad-user-fetcher.service');
const { ADSyncDatabaseService } = require('../services/ad-sync-db.service');
const logger = require('../services/logger');

/**
 * Get group IDs from environment or configuration
 * Can be set via AD_SYNC_GROUP_IDS environment variable (comma-separated)
 */
function getGroupIds() {
    const envGroupIds = process.env.AD_SYNC_GROUP_IDS || 'ad85f6c4-04af-48ad-aed6-c63e7f9f768d,a0d83e83-22c0-4406-b0b8-6eb7246aa436';
    if (envGroupIds) {
        return envGroupIds.split(',').map(id => id.trim()).filter(Boolean);
    }
    
    // Default: fetch from all groups defined in allGroupMatrix.json
    // You can modify this to use a specific set of groups
    const groupMatrixInfo = require('../services/helpers/allGroupMatrix.json');
    return Object.keys(groupMatrixInfo);
}

/**
 * Transform users data for database insertion
 */
function prepareUserGroupRelations(users) {
    const relations = [];
    
    users.forEach(user => {
        if (user.groupIds && user.groupIds.length > 0) {
            user.groupIds.forEach(groupId => {
                relations.push({
                    userId: user.id,
                    groupId: groupId
                });
            });
        }
    });
    
    return relations;
}

/**
 * Main job execution function
 */
async function run() {
    const adFetcher = new ADUserFetcherService();
    const dbService = new ADSyncDatabaseService();
    
    try {
        logger.info('Starting AD Sync Users job');
        
        // Initialize database connection
        await dbService.initialize();
        
        // Get group IDs to fetch users from
        const groupIds = getGroupIds();
        logger.info(`Fetching users from ${groupIds.length} groups`);
        
        // Fetch users from AD
        const users = await adFetcher.processGroupUsers(groupIds);
        
        if (!users || users.length === 0) {
            logger.warn('No users fetched from AD. Exiting job.');
            return;
        }
        
        logger.info(`Fetched ${users.length} users from AD`);
        
        // Prepare user-group relations
        const userGroupRelations = prepareUserGroupRelations(users);
        logger.info(`Prepared ${userGroupRelations.length} user-group relations`);
        
        // Truncate and insert users
        logger.info('Truncating AD_SYNC_USERS table...');
        await dbService.truncateUsersTable();
        
        logger.info('Inserting users into AD_SYNC_USERS table...');
        await dbService.insertUsers(users);
        
        // Truncate and insert user-group relations
        logger.info('Truncating AD_SYNC_USERS_GROUPS table...');
        await dbService.truncateUserGroupsTable();
        
        logger.info('Inserting user-group relations into AD_SYNC_USERS_GROUPS table...');
        await dbService.insertUserGroups(userGroupRelations);
        
        logger.info('AD Sync Users job completed successfully');
        
    } catch (error) {
        logger.error(`AD Sync Users job failed: ${error.message}`);
        logger.error(error.stack);
        throw error;
    } finally {
        // Close database connection
        try {
            await dbService.close();
        } catch (error) {
            logger.error(`Error closing database connection: ${error.message}`);
        }
    }
}

module.exports = { run };

