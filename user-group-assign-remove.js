require('dotenv').config();
const { getAuthToken, getUserIdByEmail, assignUserToGroup, revokeUserFromGroup } = require('./services/graph');
const logger = require('./services/logger');

/**
 * Assign and remove groups for a user
 * @param {string} userEmail - The user's email address
 * @param {Array<string>} groupsToAssign - Array of group IDs to assign
 * @param {Array<string>} groupsToRemove - Array of group IDs to remove
 */
async function assignAndRemoveGroups(userEmail, groupsToAssign = [], groupsToRemove = []) {
    try {
        logger.info(`[GROUP-ASSIGN-REMOVE] Processing user: ${userEmail}`);
        const authToken = await getAuthToken();
        if (!authToken) throw new Error('Failed to obtain Azure AD auth token');

        // Get user ID from email
        const userId = await getUserIdByEmail(userEmail, authToken);
        if (!userId) {
            logger.error(`User not found: ${userEmail}`);
            return;
        }
        logger.info(`User ID for ${userEmail}: ${userId}`);

        // Assign groups
        if (groupsToAssign.length > 0) {
            logger.info(`Assigning groups: ${groupsToAssign.join(', ')}`);
            await assignUserToGroup(userId, groupsToAssign, authToken);
        }

        // Remove groups
        if (groupsToRemove.length > 0) {
            logger.info(`Removing groups: ${groupsToRemove.join(', ')}`);
            await revokeUserFromGroup(userId, groupsToRemove, authToken);
        }

        logger.info(`[GROUP-ASSIGN-REMOVE] Completed for user: ${userEmail}`);
    } catch (error) {
        logger.error(`[GROUP-ASSIGN-REMOVE] Error: ${error.message}`);
    }
}

// CLI usage
async function main() {
    // const args = process.argv.slice(2);
    // if (args.length < 3) {
    //     console.log('Usage: node user-group-assign-remove.js <EMAIL> assignGroup1,assignGroup2 removeGroup1,removeGroup2');
    //     process.exit(1);
    // }
    const userEmail = '<EMAIL>'
    // const groupsToAssign = args[1].split(',').map(g => g.trim()).filter(Boolean);
    const groupsToAssign = [
        "c369df50-e67c-4c98-a2c1-ab36c3f317eb"
    ]
    // const groupsToRemove = args[2].split(',').map(g => g.trim()).filter(Boolean);
    const groupsToRemove = [
    //  "56961370-78b1-4b4f-9992-49e4d7b6c89a"

        // "643fcd42-10a3-4d7d-a49b-797bbf67f54e",
        // "a456f6c6-8af0-4380-8df4-5cfd34966ec8",
        // "464f21b4-0b9a-49c3-a55b-23d6416177fe",
        // "36606e9a-0110-4c7b-b49f-f10c6db67a6a",
        // "6f437b36-da26-428e-bb93-4092934daac8"
    ]



    await assignAndRemoveGroups(userEmail, groupsToAssign, groupsToRemove);
}

if (require.main === module) {
    main();
}

// Export for programmatic use
module.exports = {
    assignAndRemoveGroups
};
