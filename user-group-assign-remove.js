require('dotenv').config();
const { getAuthToken, getUserIdByEmail, assignUserToGroup, revokeUserFromGroup } = require('./services/graph');
const logger = require('./services/logger');

/**
 * Assign and remove groups for a user
 * @param {string} userEmail - The user's email address
 * @param {Array<string>} groupsToAssign - Array of group IDs to assign
 * @param {Array<string>} groupsToRemove - Array of group IDs to remove
 */
async function assignAndRemoveGroups(userEmail, groupsToAssign = [], groupsToRemove = []) {
    try {
        logger.info(`[GROUP-ASSIGN-REMOVE] Processing user: ${userEmail}`);
        const authToken = await getAuthToken();
        if (!authToken) throw new Error('Failed to obtain Azure AD auth token');

        // Get user ID from email
        const userId = await getUserIdByEmail(userEmail, authToken);
        if (!userId) {
            logger.error(`User not found: ${userEmail}`);
            return;
        }
        logger.info(`User ID for ${userEmail}: ${userId}`);

        // Assign groups
        if (groupsToAssign.length > 0) {
            logger.info(`Assigning groups: ${groupsToAssign.join(', ')}`);
            await assignUserToGroup(userId, groupsToAssign, authToken);
        }

        // Remove groups
        if (groupsToRemove.length > 0) {
            logger.info(`Removing groups: ${groupsToRemove.join(', ')}`);
            await revokeUserFromGroup(userId, groupsToRemove, authToken);
        }

        logger.info(`[GROUP-ASSIGN-REMOVE] Completed for user: ${userEmail}`);
    } catch (error) {
        logger.error(`[GROUP-ASSIGN-REMOVE] Error: ${error.message}`);
    }
}

// CLI usage
async function main() {
    // const args = process.argv.slice(2);
    // if (args.length < 3) {
    //     console.log('Usage: node user-group-assign-remove.js <EMAIL> assignGroup1,assignGroup2 removeGroup1,removeGroup2');
    //     process.exit(1);
    // }
    const userEmail = '<EMAIL>';
    // const groupsToAssign = args[1].split(',').map(g => g.trim()).filter(Boolean);
    const groupsToAssign = [
        // "c369df50-e67c-4c98-a2c1-ab36c3f317eb"
        "b465690d-ef0a-4180-828e-8467385cc0b3",
    "f81af515-cbef-4790-89ea-5ecd60e0d5a5",
    "f3b3553a-b41f-4998-9a33-b4a1465348aa",
    "c3a51461-9aaf-4ec7-8e88-066bac9d7909",
    "6ef68eb3-f6bf-4d02-b94e-9887642b3606",
    "5bb79abd-7a69-405c-9519-370c1c3c9268",
    "084322e0-96ce-4795-9ee3-0d1b76cf6153",
    "52cc3eee-1e14-4a02-a452-0c07a4c6b4c4"
    ]
    // const groupsToRemove = args[2].split(',').map(g => g.trim()).filter(Boolean);
    const groupsToRemove = [
        // "ad85f6c4-04af-48ad-aed6-c63e7f9f768d",
        // "613eaca5-6511-4233-8aec-2636f9e1c4a9",
        // "b465690d-ef0a-4180-828e-8467385cc0b3",
        // "f3b3553a-b41f-4998-9a33-b4a1465348aa",
        // "6ef68eb3-f6bf-4d02-b94e-9887642b3606",
        // "2e49b670-3e66-45cd-8517-54771843de3c",
        // "2018b370-194e-4901-bd2c-092c6c135bd9",
        // "a17dd1ed-fe7f-4553-bc3a-c872b2d339b6",
        // "ba1f17a0-ccb5-454b-a0f3-6b498b15e414",
        // "00c65867-7dd8-48f3-8d92-de1e45d5acce",
        // "f81af515-cbef-4790-89ea-5ecd60e0d5a5",
        // "52cc3eee-1e14-4a02-a452-0c07a4c6b4c4",
        // "084322e0-96ce-4795-9ee3-0d1b76cf6153",
        // "c3a51461-9aaf-4ec7-8e88-066bac9d7909",
        // "54698906-f122-4d75-9901-5d984c11a314",
        // "5bb79abd-7a69-405c-9519-370c1c3c9268",  
        // "1fc40ba9-5a74-40d1-9df8-1bce9bc4b5c7"

        // "ad85f6c4-04af-48ad-aed6-c63e7f9f768d",
        // "f3b3553a-b41f-4998-9a33-b4a1465348aa",
        // "6ef68eb3-f6bf-4d02-b94e-9887642b3606",
        // "f81af515-cbef-4790-89ea-5ecd60e0d5a5",
        // "52cc3eee-1e14-4a02-a452-0c07a4c6b4c4",
        // "084322e0-96ce-4795-9ee3-0d1b76cf6153",
        // "c3a51461-9aaf-4ec7-8e88-066bac9d7909",
        // "54698906-f122-4d75-9901-5d984c11a314",
        // "49997a9f-eb80-457f-a268-8d31e8609818",
        // "5bb79abd-7a69-405c-9519-370c1c3c9268",
        // "1fc40ba9-5a74-40d1-9df8-1bce9bc4b5c7",
        // "56961370-78b1-4b4f-9992-49e4d7b6c89a"
    ]



    await assignAndRemoveGroups(userEmail, groupsToAssign, groupsToRemove);
}

if (require.main === module) {
    main();
}

// Export for programmatic use
module.exports = {
    assignAndRemoveGroups
};
