#!/usr/bin/env python3
"""
Enhanced Python script to fetch ALL users from Azure AD with their email aliases
Uses Microsoft Graph API with pagination support
"""

import requests
import json
import os
import sys
import time
from typing import Dict, List, Optional
import csv

# Environment variables configuration
ENV_VARS = {
    'USER_CLIENT_ID': '32dcfbb1-a089-4749-8c57-ce11485a617b',
    'USER_CLIENT_SECRET': '****************************************',
    'USER_TENANT_ID': '6926239f-3483-4451-8452-48ee3bee086f',
    'USER_GRAPH_ENDPOINT': 'https://graph.microsoft.com'
}

class AzureADUserFetcher:
    """Enhanced Azure AD user fetcher with pagination and alias extraction"""
    
    def __init__(self):
        """Initialize the Azure AD client"""
        self.client_id = os.getenv('USER_CLIENT_ID', ENV_VARS['USER_CLIENT_ID'])
        self.client_secret = os.getenv('USER_CLIENT_SECRET', ENV_VARS['USER_CLIENT_SECRET'])
        self.tenant_id = os.getenv('USER_TENANT_ID', ENV_VARS['USER_TENANT_ID'])
        self.graph_endpoint = os.getenv('USER_GRAPH_ENDPOINT', ENV_VARS['USER_GRAPH_ENDPOINT'])
        
        # Validate required environment variables
        if not all([self.client_id, self.client_secret, self.tenant_id]):
            print("Error: Missing required environment variables")
            sys.exit(1)
            
        self.access_token = None
        self.token_expires_at = 0
    
    def get_access_token(self) -> str:
        """Acquire access token using client credentials flow"""
        try:
            # Check if we have a valid cached token
            if self.access_token and time.time() < self.token_expires_at:
                return self.access_token
                
            print("Acquiring new access token...")
            
            # Token endpoint URL
            token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
            
            # Request headers
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # Request body for client credentials flow
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'scope': f'{self.graph_endpoint}/.default',
                'grant_type': 'client_credentials'
            }
            
            # Make token request
            response = requests.post(token_url, headers=headers, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            
            # Calculate token expiration (subtract 5 minutes for safety)
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = time.time() + expires_in - 300
            
            print("✓ Access token acquired successfully")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            print(f"Error acquiring access token: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise
    
    def make_graph_request(self, url: str) -> Dict:
        """Make authenticated request to Microsoft Graph API"""
        try:
            # Get access token
            token = self.get_access_token()
            
            # Prepare headers
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # Make request
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Graph API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise
    
    def fetch_all_users(self, page_size: int = 999) -> List[Dict]:
        """Fetch all users from Azure AD with pagination"""
        all_users = []
        next_link = None
        page_count = 0
        
        try:
            print("🔄 Starting to fetch all users from Azure AD...")
            
            # Build initial URL
            base_url = f"{self.graph_endpoint}/v1.0/users"
            params = [
                '$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,businessPhones,mobilePhone,createdDateTime,lastSignInDateTime,userType',
                f'$top={page_size}',
                '$orderby=displayName'
            ]
            url = f"{base_url}?{'&'.join(params)}"
            
            while True:
                page_count += 1
                print(f"📄 Fetching page {page_count}...")
                
                # Use next link if available, otherwise use constructed URL
                current_url = next_link if next_link else url
                response = self.make_graph_request(current_url)
                
                # Extract users from response
                users = response.get('value', [])
                all_users.extend(users)
                
                print(f"   Retrieved {len(users)} users (Total so far: {len(all_users)})")
                
                # Check for next page
                next_link = response.get('@odata.nextLink')
                if not next_link:
                    break
                    
                # Small delay to avoid rate limiting
                time.sleep(0.5)
            
            print(f"\n✅ Successfully retrieved {len(all_users)} total users from Azure AD")
            return all_users
            
        except Exception as e:
            print(f"❌ Error fetching users: {e}")
            return all_users  # Return what we have so far
    
    def extract_user_aliases(self, user: Dict) -> Dict:
        """Extract all email aliases for a user"""
        aliases = set()
        
        # Add primary email
        if user.get('mail'):
            aliases.add(user['mail'].lower())
        
        # Add UPN
        if user.get('userPrincipalName'):
            aliases.add(user['userPrincipalName'].lower())
        
        # Extract from proxy addresses
        proxy_addresses = user.get('proxyAddresses', [])
        for proxy in proxy_addresses:
            if isinstance(proxy, str) and (proxy.startswith('smtp:') or proxy.startswith('SMTP:')):
                email = proxy[5:]  # Remove 'smtp:' or 'SMTP:' prefix
                aliases.add(email.lower())
        
        return {
            'id': user.get('id'),
            'displayName': user.get('displayName'),
            'primaryEmail': user.get('mail'),
            'userPrincipalName': user.get('userPrincipalName'),
            'accountEnabled': user.get('accountEnabled'),
            'userType': user.get('userType'),
            'department': user.get('department'),
            'jobTitle': user.get('jobTitle'),
            'officeLocation': user.get('officeLocation'),
            'businessPhones': user.get('businessPhones', []),
            'mobilePhone': user.get('mobilePhone'),
            'createdDateTime': user.get('createdDateTime'),
            'lastSignInDateTime': user.get('lastSignInDateTime'),
            'aliases': sorted(list(aliases)),
            'aliasCount': len(aliases),
            'proxyAddresses': proxy_addresses
        }
    
    def process_all_users(self, output_format: str = 'json') -> List[Dict]:
        """Fetch all users and process their aliases"""
        print("🚀 Fetching all users with their email aliases...")
        
        # Get all users
        all_users = self.fetch_all_users()
        
        if not all_users:
            print("❌ No users found or error occurred")
            return []
        
        # Process each user to extract aliases
        users_with_aliases = []
        print(f"\n🔄 Processing {len(all_users)} users to extract aliases...")
        
        for i, user in enumerate(all_users, 1):
            if i % 100 == 0:
                print(f"   Processed {i}/{len(all_users)} users...")
            
            user_data = self.extract_user_aliases(user)
            users_with_aliases.append(user_data)
        
        print(f"✅ Processed all {len(users_with_aliases)} users")
        
        return users_with_aliases
    
    def save_to_file(self, users_data: List[Dict], filename: str, format_type: str = 'json'):
        """Save users data to file in specified format"""
        try:
            if format_type.lower() == 'json':
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(users_data, f, indent=2, ensure_ascii=False)
                print(f"✅ Results saved to {filename}")
                
            elif format_type.lower() == 'csv':
                if not users_data:
                    print("❌ No data to save")
                    return
                    
                # Flatten the data for CSV
                csv_data = []
                for user in users_data:
                    row = {
                        'ID': user.get('id', ''),
                        'Display Name': user.get('displayName', ''),
                        'Primary Email': user.get('primaryEmail', ''),
                        'UPN': user.get('userPrincipalName', ''),
                        'Account Enabled': user.get('accountEnabled', ''),
                        'User Type': user.get('userType', ''),
                        'Department': user.get('department', ''),
                        'Job Title': user.get('jobTitle', ''),
                        'Office Location': user.get('officeLocation', ''),
                        'Business Phones': '; '.join(user.get('businessPhones', [])),
                        'Mobile Phone': user.get('mobilePhone', ''),
                        'Created Date': user.get('createdDateTime', ''),
                        'Last Sign In': user.get('lastSignInDateTime', ''),
                        'Alias Count': user.get('aliasCount', 0),
                        'All Aliases': '; '.join(user.get('aliases', [])),
                        'Proxy Addresses': '; '.join(user.get('proxyAddresses', []))
                    }
                    csv_data.append(row)
                
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    if csv_data:
                        writer = csv.DictWriter(f, fieldnames=csv_data[0].keys())
                        writer.writeheader()
                        writer.writerows(csv_data)
                print(f"✅ Results saved to {filename}")
                
        except Exception as e:
            print(f"❌ Error saving to file: {e}")
    
    def print_summary(self, users_data: List[Dict]):
        """Print a summary of users and their aliases"""
        if not users_data:
            print("❌ No users to summarize")
            return
        
        print(f"\n{'='*80}")
        print(f"📊 USER SUMMARY")
        print(f"{'='*80}")
        print(f"Total Users: {len(users_data)}")
        
        # Count statistics
        enabled_users = sum(1 for u in users_data if u.get('accountEnabled'))
        guest_users = sum(1 for u in users_data if u.get('userType') == 'Guest')
        users_with_multiple_aliases = sum(1 for u in users_data if u.get('aliasCount', 0) > 1)
        total_aliases = sum(u.get('aliasCount', 0) for u in users_data)
        
        print(f"Enabled Users: {enabled_users}")
        print(f"Disabled Users: {len(users_data) - enabled_users}")
        print(f"Guest Users: {guest_users}")
        print(f"Users with Multiple Aliases: {users_with_multiple_aliases}")
        print(f"Total Email Aliases: {total_aliases}")
        print(f"Average Aliases per User: {total_aliases / len(users_data):.2f}")
        
        # Show users with most aliases
        print(f"\n{'='*80}")
        print(f"🏆 TOP 10 USERS WITH MOST ALIASES")
        print(f"{'='*80}")
        
        sorted_users = sorted(users_data, key=lambda x: x.get('aliasCount', 0), reverse=True)
        for i, user in enumerate(sorted_users[:10], 1):
            print(f"{i:2d}. {user.get('displayName', 'N/A'):<40} ({user.get('aliasCount', 0)} aliases)")
            for alias in user.get('aliases', [])[:3]:  # Show first 3 aliases
                print(f"     📧 {alias}")
            if user.get('aliasCount', 0) > 3:
                print(f"     ... and {user.get('aliasCount', 0) - 3} more")
            print()


def main():
    """Main function"""
    print("🔍 Azure AD User Fetcher - Get ALL users with email aliases")
    print("=" * 60)
    
    # Initialize the fetcher
    fetcher = AzureADUserFetcher()
    
    # Parse command line arguments
    output_format = 'json'
    output_file = 'all_users_with_aliases'
    
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['--csv', '-c']:
            output_format = 'csv'
            output_file += '.csv'
        elif sys.argv[1].lower() in ['--json', '-j']:
            output_format = 'json'
            output_file += '.json'
        elif sys.argv[1].lower() in ['--help', '-h']:
            print_help()
            return
        else:
            output_file = sys.argv[1]
            if output_file.endswith('.csv'):
                output_format = 'csv'
            elif output_file.endswith('.json'):
                output_format = 'json'
    else:
        output_file += '.json'
    
    # Fetch and process all users
    users_data = fetcher.process_all_users()
    
    if users_data:
        # Save to file
        fetcher.save_to_file(users_data, output_file, output_format)
        
        # Print summary
        fetcher.print_summary(users_data)
        
        print(f"\n🎉 Complete! Check '{output_file}' for detailed results.")
    else:
        print("❌ No users were retrieved.")


def print_help():
    """Print help information"""
    print("""
🔍 Azure AD User Fetcher - Help

Usage:
    python fetch_all_users.py [OPTIONS] [FILENAME]

Options:
    --json, -j          Save as JSON format (default)
    --csv, -c           Save as CSV format
    --help, -h          Show this help message

Examples:
    # Save as JSON (default)
    python fetch_all_users.py
    
    # Save as CSV
    python fetch_all_users.py --csv
    
    # Save to custom filename
    python fetch_all_users.py my_users.json
    python fetch_all_users.py my_users.csv

Features:
    ✅ Fetches ALL users from Azure AD (with pagination)
    ✅ Extracts all email aliases (mail, UPN, proxy addresses)
    ✅ Provides detailed user information
    ✅ Supports JSON and CSV output formats
    ✅ Shows summary statistics
    ✅ Handles rate limiting and errors gracefully
    """)


if __name__ == "__main__":
    main()
