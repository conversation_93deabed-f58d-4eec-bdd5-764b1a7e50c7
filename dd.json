{"language": "EN", "indicatorTools": [{"id": "export-png", "disabled": true, "label": "Export PNG"}, {"id": "export-csv", "label": "Export CSV"}], "multiDrivers": true, "indicatorDrivers": [{"title": "Non Oil GDP Constant_INDEX", "type": "radio", "id": "parameter_1_range", "subtitle": "Range", "options": [{"label": "-8%", "value": "very low", "isSelected": false}, {"label": "-4%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "4%", "value": "high", "isSelected": false}, {"label": "8%", "value": "very high", "isSelected": false}]}, {"title": "PMI-Q", "type": "radio", "id": "parameter_2_range", "subtitle": "Range", "options": [{"label": "-3%", "value": "very low", "isSelected": false}, {"label": "-1.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "1.5%", "value": "high", "isSelected": false}, {"label": "3%", "value": "very high", "isSelected": false}]}, {"title": "Business Confidence index", "id": "parameter_3_range", "type": "radio", "subtitle": "Range", "note": "", "options": [{"label": "-8%", "value": "very low", "isSelected": false}, {"label": "-4%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "4%", "value": "high", "isSelected": false}, {"label": "8%", "value": "very high", "isSelected": false}]}, {"title": "CPI", "type": "radio", "id": "parameter_4_range", "subtitle": "Range", "options": [{"label": "-3%", "value": "very low", "isSelected": false}, {"label": "-1.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "1.5%", "value": "high", "isSelected": false}, {"label": "3%", "value": "very high", "isSelected": false}]}, {"title": "IPI", "type": "radio", "id": "parameter_5_range", "subtitle": "Range", "options": [{"label": "-6%", "value": "very low", "isSelected": false}, {"label": "-3%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "3%", "value": "high", "isSelected": false}, {"label": "6%", "value": "very high", "isSelected": false}]}, {"title": "Compensation of employees_Total", "type": "radio", "id": "parameter_6_range", "subtitle": "Range", "options": [{"label": "-3%", "value": "very low", "isSelected": false}, {"label": "-1.5%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "1.5%", "value": "high", "isSelected": false}, {"label": "3%", "value": "very high", "isSelected": false}]}, {"title": "Fixed Capital Formation_Total", "type": "radio", "id": "parameter_7_range", "subtitle": "Range", "options": [{"label": "-4%", "value": "very low", "isSelected": false}, {"label": "-2%", "value": "low", "isSelected": false}, {"label": "No change", "value": "medium", "isSelected": true}, {"label": "2%", "value": "high", "isSelected": false}, {"label": "4%", "value": "very high", "isSelected": false}]}], "indicatorValues": {"multiValuesMeta": [[{"id": "current-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "number_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "estimate", "title": "% change {dateStart} to {dateEnd}", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "quarter-index", "title": "Indicator as of {dateStart}", "type": "static-with-title-template", "valueFormat": "number_1.1-1", "templateFormat": "yyyy", "invertColor": true}, {"id": "last-nowcast-first-forecast", "title": "% change {dateStart} to {dateEnd} ", "type": "dynamic-with-title-template", "valueFormat": "percentage_1.1-1", "templateFormat": "yyyy", "invertColor": true}]], "overviewValuesMeta": []}, "indicatorVisualizations": {"visualizationsMeta": [{"id": "line-chart-economy-ur-constant", "isInflation": true, "vizLabel": "UR Label", "sortOrder": 1, "type": "line-chart", "invertColor": true, "comboIdTable": "VW_DS_COMBINATION_DF_UNEMP_ECONOMY", "viewName": "VW_DS_WHATIF_UNEMPLOYEMNT_MACROECONOMIC_SCENARIO", "seriesMeta": [{"id": "total", "label": "Unemployment Rate index", "color": "#3667ff", "type": "solid", "dimension": {"TYPE": "NOWCAST", "CITIZENSHIP": "Total"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "total-forecast", "label": "Unemployment Rate index", "color": "#3667ff", "type": "forecast-with-arrow", "dimension": {"TYPE": "FORECAST", "CITIZENSHIP": "Total"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "emirati", "label": "Unemployment Rate index", "color": "#3667ff", "type": "solid", "dimension": {"TYPE": "NOWCAST", "CITIZENSHIP": "Emirati"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "emirati-forecast", "label": "Unemployment Rate index", "color": "#3667ff", "type": "forecast-with-arrow", "dimension": {"TYPE": "FORECAST", "CITIZENSHIP": "Emirati"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "non-Emirati", "label": "Unemployment Rate index", "color": "#3667ff", "type": "solid", "dimension": {"TYPE": "NOWCAST", "CITIZENSHIP": "Non-Emirati"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}, {"id": "non-Emirati-forecast", "label": "Unemployment Rate index", "color": "#3667ff", "type": "forecast-with-arrow", "dimension": {"TYPE": "FORECAST", "CITIZENSHIP": "Non-Emirati"}, "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}}], "markersMeta": [{"id": "economic-index_real-vs-forecast", "color": "#ffffff", "type": "line-with-label", "labelText": "Forecast", "axis": "x", "accessor": {"type": "date", "path": "DATE", "specifier": "%Y-%m-%d"}}], "showInterval": true, "showQuarterlyIntervals": false, "showPointLabels": true, "xAxisLabel": null, "timeUnit": ["Yearly"], "yAxisLabel": "Index", "yAxisExtraStepMin": 0.005, "yAxisExtraStepMax": 0.005, "xAxisFormat": "date_y", "yAxisFormat": "d3-number", "tooltipTitleFormat": "date_y", "tooltipValueFormat": "number_1.1-1"}], "visualizationDefault": "line-chart-economy-inflation-constant"}, "indicatorType": "analytical-apps", "unit": "%"}