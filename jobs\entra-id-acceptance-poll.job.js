require('dotenv').config();

const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
const { decryptEmail } = require('../services/encryption.service');
const { getUserData, setUserActivationStatus, getDisseminationAccessPolicy, createUserAccessRequest, createUserAccess, createUserAccessApproval, updateUserAccessRequestEntraIdStatus, updateUserAccessRequestStatus, updateUserData } = require('../services/executeQuery.service');
const { inviteStatusEntraIdUser, assignUserToGroup, checkUserInActiveDirectory } = require('../services/graph');
const { getBayaanExternalGroupMatrix, getDomainMatrixMap, getInverseDomainMatrixMap } = require('../services/group.service');
const logger = require('../services/logger');
const { v4: uuidv4 } = require("uuid");
const { syncUser, sendWelcomeEmail } = require('../services/mobile');

async function initDatabase() {
    logger.debug('Entering initDatabase function.');
    logger.info('Initializing database module.');
    try {
        await pgSequelize.authenticate();
        logger.info('Database initialized successfully.');
        logger.debug('Exiting initDatabase function successfully.');
    } catch (err) {
        logger.error(`Error during database initialization: ${err.message}`);
        throw err; // Re-throw the error after logging it for proper error handling upstream
    }
}



/**
 * Polls Entra ID invite acceptance status
 */
async function entraIdInviteAcceptancePoll() {
    try {
        logger.info('[ENTRAIDINVITEACCEPTPOLL CRON] Polling Invite Status start');
        const groupMatrix = getBayaanExternalGroupMatrix();
        const domainMatrixMap = await getDomainMatrixMap();
        const invDomainMatrixMap = getInverseDomainMatrixMap();

        const entraIdInviteSentRequests = await getUserData({
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG: 'ENTRA_ID_INVITE_SENT'
        });

        logger.info('Fetched users with ENTRA ID invite sent status.', { userCount: entraIdInviteSentRequests.length });

        const requestPromises = [];

        for (const request of entraIdInviteSentRequests) {
            try{
                const decryptedEmail = decryptEmail(request.EMAIL);
                let proceedAssign = false
                let entraUserId = null
                if(+request.EXISTING_USER==1 || decryptedEmail.includes('@scad.gov.ae')){
                    proceedAssign = true
                    const entraIdUserData = await checkUserInActiveDirectory(decryptedEmail)
                    if (entraIdUserData != null){
                        entraUserId = entraIdUserData.id
                    }
                }
                else{
                    const entraIdStatus = await inviteStatusEntraIdUser(decryptedEmail);
                    logger.debug(`Invite status for user ${decryptedEmail}: ${JSON.stringify(entraIdStatus)}`);
                    if (entraIdStatus.inviteStatus && entraIdStatus.accountEnabled) {
                        proceedAssign = true
                        entraUserId = entraIdStatus.id
                    }
                    logger.info(`[ENTRAIDINVITEACCEPTPOLL CRON] User ${decryptedEmail} has accepted invite`);
                }

                if (proceedAssign){

                    // Assign groups for Superusers based on Dissemination Policy
                    if (['PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER', 'DG'].includes(request.ROLE)) {
                        const accessPolicies = await getDisseminationAccessPolicy(request.ENTITY_ID);
                        logger.debug(`Access policies for entity ${request.ENTITY_ID}: ${JSON.stringify(accessPolicies)}`);
                        //Matrix groups based on Dissemination Policy
                        const matrix_groups = accessPolicies.map(policy => 
                            `MATRIX__${domainMatrixMap[policy.DOMAIN.toUpperCase()]}__${policy.CLASSIFICATION.toUpperCase()}`
                        );

                        //Default confidential access for all the entities according to dissemination policy
                        let defaultAccess =Object.values(groupMatrix).filter(g=>g.includes("CONFIDENTIAL") || g.includes("OPEN"))

                        //Combines both default access and matrix groups
                        const combinedAccess = defaultAccess.concat(matrix_groups)
                        //Removes OPEN access from the combined access
                        groups_to_be_assigned = [... new Set(combinedAccess)]
                        logger.info(`Groups to be assigned to ${decryptedEmail}: [${groups_to_be_assigned.join(', ')}]`);

                        const matrixGroupIds = groups_to_be_assigned.map(g => {
                            const groups = Object.entries(groupMatrix)
                                .filter(([id, name]) => g === name)
                                .map(([id, name]) => id);

                            if (groups.length) {
                                return groups[groups.length - 1];
                            } else {
                                logger.warn(`No matching group found for ${g}`);
                            }
                        }).filter(g => g);
                        
                        let userData = await getUserData({"EMAIL":request.EMAIL})
                        if (!userData.length) {
                            logger.error(`User not found in database: ${decryptedEmail}`);
                            throw new Error(`User not found in database: ${decryptedEmail}`);
                        }
                        userData = userData[0];
                        const userId = userData.ID;
                        const requestId = uuidv4();
                        await createUserAccessRequest(requestId, userId)
                        await Promise.all(matrixGroupIds.map(async groupId => {
                            const groupName = groupMatrix[groupId];
                            let domain = groupName.split('__')[1];
                            domain = invDomainMatrixMap[domain]
                            let classification = groupName.split('__')[2];
                            const userAccessId = uuidv4();
                            await createUserAccess(requestId, userAccessId, userId, domain, classification)
                            await createUserAccessApproval(uuidv4(), userAccessId, 'APPROVED', null)
                        }))
                        
                        if (matrixGroupIds.length) {

                            let platformAccessGroups = []
                            if (process.env.NODE_ENV === 'prod') {
                                if (decryptedEmail.includes('@scad.gov.ae')) {
                                    platformAccessGroups = ['ad85f6c4-04af-48ad-aed6-c63e7f9f768d'] //Prod Internal
                                }
                                else{                          
                                    platformAccessGroups = ['a0d83e83-22c0-4406-b0b8-6eb7246aa436'] //Prod External
                                }
                            }
                            else{
                                platformAccessGroups = ['93732bd5-d769-4b9a-bb0e-2e414a50b463'] //Staging Internal
                            }

                            let addedExternalGroups = matrixGroupIds.concat(platformAccessGroups)
                            await assignUserToGroup(entraUserId, addedExternalGroups);
                            await updateUserAccessRequestEntraIdStatus(requestId, 'COMPLETED');
                            await updateUserAccessRequestStatus(requestId, 'COMPLETED');
                            
                            logger.info(`Groups assigned to ${decryptedEmail}: [${addedExternalGroups.join(', ')}]`);

                            // Sync user with mobile and trigger welcome Email
                            if (
                              request.MOBILE_SYNC_STATUS == "PENDING" &&
                              request.MOBILE_SYNC_FAILED_ATTEMPTS < 5
                            ) {
                              await syncUser(request);
                            } else if (
                              request.MOBILE_SYNC_STATUS == "NA_ONLY_WELCOME_EMAIL"
                            ) {
                              const emailData = {
                                name: request.NAME,
                                email: decryptEmail(request.EMAIL),
                                role: request.ROLE,
                              };
                              const sent = await sendWelcomeEmail(
                                emailData,
                                {}
                              );
                              if (sent) {
                                await updateUserData("EMAIL", request.EMAIL, {
                                  MOBILE_SYNC_STATUS: "NA_WITH_EMAIL_SENT",
                                });
                              }
                            }
                        }
                    }
                    
                    await setUserActivationStatus(request.EMAIL, 'ACTIVE');
                    logger.info(`User activation status set to ACTIVE for ${decryptedEmail}`);
                    // Assign groups for users will be done using another job on request basis

                }
                else{
                    logger.info(`[ENTRAIDINVITEACCEPTPOLL CRON] User ${decryptedEmail} has not accepted invite`);
                }
            }
            catch (err) {
                logger.error(`Error during ENTRA ID invite acceptance poll: ${err.message}`);
            }
        }

        await Promise.all(requestPromises);
        logger.info('[ENTRAIDINVITEACCEPTPOLL CRON] Polling Invite Status completed');
    } catch (err) {
        logger.error(`Error during ENTRA ID invite acceptance poll: ${err.message}`);
        throw err;
    }
}

const run = async () => {
    await initDatabase();
  
    try {
        await entraIdInviteAcceptancePoll();
        logger.info("Entra ID Acceptance completed.");
    } catch (err) {
        logger.error(`Failed to check Entra ID Acceptance: ${err.message}`);
    }
};

module.exports = {
    run
};
