/**
 * <PERSON><PERSON><PERSON> to add new Dissemination Policy records for given entity(s)
 * 
 * The script currently does only insert operations, it can be updated to perform
 * upsert operations in later stage.
 * 
 * Ensure the policy records are updated in the `data.json` file in this module folder.
 * Format:
 * [{
    "entityId": "E50",
    "entityName": "Abu Dhabi Executive Council",
    "domain": "Agriculture & Environment",
    "highestClassification": "SECRET"
  }]
 */
const { log } = require("winston");
const database = require("../../services/database.service");
const logger = require("../../services/logger");
const { bulkAddPolicyRecords } = require("./services/executeQuery.service");

require("dotenv").config();

const CLASSIFICATIONS = ["OPEN", "CONFIDENTIAL", "SENSITIVE", "SECRET"];
const CLASSIFICATION_RANK = {
  OPEN: 1,
  CONFIDENTIAL: 2,
  SENSITIVE: 3,
  SECRET: 4,
};

async function initDatabase() {
  logger.debug("Entering initDatabase function.");
  logger.info("Initializing database module.");
  try {
    await database.initialize();
    logger.info("Database initialized successfully.");
    logger.debug("Exiting initDatabase function successfully.");
  } catch (err) {
    logger.error(`Error during database initialization: ${err.message}`);
    throw err;
  }
}

function getClassificationsUpTo(level) {
  const maxRank = CLASSIFICATION_RANK[level.toUpperCase()];
  return CLASSIFICATIONS.filter(
    (classification) => CLASSIFICATION_RANK[classification] <= maxRank
  );
}

const run = async () => {
  await initDatabase();

  try {
    const newPolicyData = require("./data.json");

    for (const policy of newPolicyData) {
      const { entityId, entityName, domain, highestClassification } = policy;
      const entityClassifications = getClassificationsUpTo(
        highestClassification
      );
      logger.info(
        `Classifications to be inserted for ${domain}: ${entityClassifications} `
      );
      await bulkAddPolicyRecords(
        entityId,
        entityName,
        domain,
        entityClassifications
      );
    }

    logger.info("Dissemination Policy Complete");
  } catch (err) {
    logger.error(`Failed to update dissemination policy: ${err.message}`);
  }
};

module.exports = {
  run,
};
