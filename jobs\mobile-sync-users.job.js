require('dotenv').config();

const { sequelize: pgSequelize } = require("../database/postgres/platform-db/models/index");
const { getUserData } = require('../services/executeQuery.service');
const logger = require('../services/logger');
const { syncUser } = require('../services/mobile');

async function initDatabase() {
    logger.debug('Entering initDatabase function.');
    logger.info('Initializing database module.');
    try {
        await pgSequelize.authenticate();
        logger.info('Database initialized successfully.');
        logger.debug('Exiting initDatabase function successfully.');
    } catch (err) {
        logger.error(`Error during database initialization: ${err.message}`);
        throw err; // Re-throw the error after logging it for proper error handling upstream
    }
}


async function mobileUserSync() {
    try {
        logger.info('[MOBILE SYNC CRON] Syncing users to mobile start');
        // Fetch approved requests from SANADKOM database table
        let pendingUsers = await getUserData({
            ROLE: ['PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER','DG','USER'],
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG: 'ACTIVE',
            MOBILE_SYNC: null,
            ID: "8e980bf7-86e4-488d-b2a3-8cfa6a6f6e2f"
        });

        if (pendingUsers.length) {
            let requestPromises = [];
            for (const user of pendingUsers) {
                requestPromises.push(syncUser(user));
                logger.debug(`Added invite promise for superuser: ${user.EMAIL}`);
            }
            await Promise.all(requestPromises);
            logger.info('[ENTRAIDINVITER CRON] All Superuser invitations sent successfully');
        } 
        else {
            logger.info('[ENTRAIDINVITER CRON] No Pending Superusers found');
        }
        logger.info('[ENTRAIDINVITER CRON] Invite Superuser Users end');
    } catch (err) {
        logger.error(`Error in superuserEntraIdInviter: ${err.message}`);
        throw err;
    }
}


const run = async () => {
    await initDatabase();
  
    try {
        // await mobileUserSync();
        logger.info("Mobile User Sync completed.");
    } catch (err) {
        logger.error(`Failed to sync users to mobile: ${err.message}`);
    }

};

module.exports = {
    run
};