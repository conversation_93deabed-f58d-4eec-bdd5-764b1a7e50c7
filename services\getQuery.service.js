const logger = require('../services/logger');
//Done
async function getADUserUpdateQuery(data) {
    try {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('Data must be a non-empty array');
        }

        const tableName = '"IFP_USER_INFO"';
        const columns = [
            '"ID"', '"NAME"', '"EMAIL"', '"ENTITY"', '"DEPARTMENT"', 
            '"AD_GROUP"', '"DOMAIN"', '"CLASSIFICATION"', '"USER_TYPE"', 
            '"USER_CREATED_DT"', '"ENC_EMAIL"'
        ];

        let binds = {};
        let valueColumnMap = {
            "ID": "id",
            "NAME": "name",
            "EMAIL": "email",
            "DEPARTMENT": "department",
            "USER_CREATED_DT": "createdDateTime",
            "ENC_EMAIL": "encEmail"
        };

        let query = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES `;
        let bindCount = 0;
        let valuesArr = [];

        data.forEach(groupData => {
            groupData.members.forEach((row) => {
                let valuePlaceholders = [];

                columns.forEach((col) => {
                    let bindKey = `bind${bindCount}`;

                    if (col === '"ENTITY"') {
                        let entity = "";
                        if (row['email']) {
                            entity = row["email"].split('@')[1].split('.')[0];
                            if (entity) entity = entity.toUpperCase();
                        }
                        binds[bindKey] = entity ? entity : "";

                    } else if (col === '"AD_GROUP"') {
                        binds[bindKey] = groupData.name;

                    } else if (col === '"DOMAIN"') {
                        let splitGroup = groupData.name.split('__');
                        binds[bindKey] = splitGroup ? splitGroup[1] : '';

                    } else if (col === '"CLASSIFICATION"') {
                        let classification = groupData.name.split('__').pop();
                        binds[bindKey] = ["OPEN", "SENSITIVE", "CONFIDENTIAL", "SECRET"].includes(classification) ? classification : null;

                    } else if (col === '"USER_TYPE"') {
                        binds[bindKey] = row["email"] && !row["email"].includes("scad.gov.ae") ? "EXTERNAL" : "INTERNAL";

                    } else if (col === '"USER_CREATED_DT"') {
                        binds[bindKey] = new Date(row[valueColumnMap["USER_CREATED_DT"]]);

                    } else {
                        binds[bindKey] = row[valueColumnMap[col.replace(/"/g, '')]];
                    }

                    valuePlaceholders.push(`:${bindKey}`);
                    bindCount++;
                });

                valuesArr.push(`(${valuePlaceholders.join(', ')})`);
            });
        });

        query += valuesArr.join(', ');
        
        Object.keys(binds).forEach(key => {
        if (binds[key] === undefined) {
          binds[key] = null;
        }
      });
        return { query, binds };
    } catch (err) {
        logger.error(`Error in getADUserUpdateQuery: ${err}`);
        throw err;
    }
}
//Done
async function truncateADUserListQuery() {
    try {
        // Postgres truncate with case-sensitive table name
        let query = 'TRUNCATE TABLE "IFP_USER_INFO"';
        return query;
    } catch (err) {
        logger.error(`Error in truncateADUserListQuery: ${err}`);
        throw err;
    }
}

//Done
async function getUserDataQuery(filters) {
    try {
        let query = 'SELECT * FROM "IFP_FLOW_USERS_V2"';
        const binds = {};
        const conditions = [];

        Object.keys(filters).forEach((field) => {
            const values = filters[field];
            const quotedField = `"${field}"`; // 👈 enforce case sensitivity

            if (Array.isArray(values)) {
                const placeholders = values
                    .map((_, index) => `:${field}${index}`)
                    .join(", ");
                conditions.push(`${quotedField} IN (${placeholders})`);

                values.forEach((value, index) => {
                    binds[`${field}${index}`] = value;
                });

            } else {
                if (values == null) {
                    conditions.push(`${quotedField} IS NULL`);
                } else {
                    conditions.push(`${quotedField} = :${field}`);
                    binds[field] = values;
                }
            }
        });

        if (conditions.length > 0) {
            query += " WHERE " + conditions.join(" AND ");
        }

        return { query, binds };
    } catch (err) {
        logger.error(`Error in getUserDataQuery: ${err}`);
        throw err;
    }
}

//Done
async function setUserActivationStatusQuery(userId, status) {
    try {
        const query = `
            UPDATE "IFP_FLOW_USERS_V2"
            SET "ACTIVATION_FLAG" = :status,
                "UPDATED_AT" = CURRENT_TIMESTAMP
            WHERE "EMAIL" = :userId
        `;
        const binds = {
            userId,
            status
        };

        return { query, binds };
    } catch (err) {
        logger.error(`Error in setUserActivationStatusQuery: ${err}`);
        throw err;
    }
}
//Done  
async function getDisseminationAccessPolicyQuery(entityId) {
    try {
        const query = `
            SELECT "DOMAIN", "CLASSIFICATION"
            FROM "IFP_DISS_ACCESS_POLICY"
            WHERE "ENTITY_ID" = :entityId
        `;
        const binds = {
            entityId: entityId ?? null, // ensure undefined → null
        };

        logger.info('Constructed dissemination access policy query successfully.', {
            entityId,
            query,
        });

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error constructing dissemination access policy query: ${err.message}`,
            { entityId }
        );
        throw err;
    }
}
//Done
async function getUserAccessRequestsQuery(filters) {
    try {
        logger.info('Constructing query to fetch user access requests.', { filters });

        let query = 'SELECT * FROM "IFP_USER_ACCESS_REQUEST"';
        const binds = {};
        const conditions = [];

        Object.keys(filters || {}).forEach((field) => {
            const values = filters[field];
            const quotedField = `"${field}"`;

            if (Array.isArray(values)) {
                if (values.length === 0) return; // skip empty arrays

                const placeholders = values.map((_, index) => `:${field}${index}`).join(', ');
                conditions.push(`${quotedField} IN (${placeholders})`);

                values.forEach((value, index) => {
                    binds[`${field}${index}`] = value;
                });

            } else {
                if (values == null) {
                    conditions.push(`${quotedField} IS NULL`);
                } else {
                    conditions.push(`${quotedField} = :${field}`);
                    binds[field] = values;
                }
            }
        });

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY "CREATED_DT"'

        logger.info('Query constructed for fetching user access requests.', { query, binds });

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error constructing query for user access requests: ${err.message}`,
            { filters }
        );
        throw err;
    }
}
//Done
async function getUserAccessLevelsQuery(filters) {
    try {
        logger.info('Constructing query to fetch user access levels.', { filters });

        let query = 'SELECT * FROM "IFP_USER_ACCESS"';
        const binds = {};
        const conditions = [];

        // Quote identifier unless it's already quoted
        const quoteIdent = (name) =>
            typeof name === 'string' && name.startsWith('"') && name.endsWith('"')
                ? name
                : `"${name}"`;

        Object.keys(filters || {}).forEach((field) => {
            const values = filters[field];
            const quotedField = quoteIdent(field);

            if (Array.isArray(values)) {
                if (values.length === 0) return; // skip empty arrays to avoid IN ()
                const placeholders = values
                    .map((_, index) => `:${field}${index}`)
                    .join(', ');
                conditions.push(`${quotedField} IN (${placeholders})`);
                values.forEach((value, index) => {
                    binds[`${field}${index}`] = value;
                });
            } else {
                if (values == null) {
                    conditions.push(`${quotedField} IS NULL`);
                } else {
                    conditions.push(`${quotedField} = :${field}`);
                    binds[field] = values;
                }
            }
        });

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        logger.info('Query constructed for fetching user access levels.', { query, binds });

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error constructing query for user access levels: ${err.message}`,
            { filters }
        );
        throw err;
    }
}
//Done
async function updateUserAccessRequestEntraIdStatusQuery(requestId, status) {
  try {
      logger.info('Constructing query to update user access request Entra ID status.', { requestId, status });

      const query = `
          UPDATE "IFP_USER_ACCESS_REQUEST"
          SET "ENTRA_ID_ASSIGN_STATUS" = :status,
              "ENTRA_ID_ASSIGN_DT" = CURRENT_TIMESTAMP
          WHERE "REQUEST_ID" = :requestId
      `;

      const binds = {
          requestId: requestId ?? null, // ensure undefined → null
          status: status ?? null
      };

      logger.info('Query constructed for updating user access request status.', { query, binds });

      return { query, binds };
  } catch (err) {
      logger.error(
          `Error constructing query for updating user access request status: ${err.message}`,
          { requestId, status }
      );
      throw err;
  }
}
//Done
async function updateUserAccessRequestStatusQuery(requestId, status) {
    try {
        logger.info('Constructing query to update user access request status.', { requestId, status });

        const query = `
            UPDATE "IFP_USER_ACCESS_REQUEST"
            SET "STATUS" = :status
            WHERE "REQUEST_ID" = :requestId
        `;

        const binds = {
            requestId: requestId ?? null, // ensure undefined → null
            status: status ?? null
        };

        logger.debug('Query constructed for updating user access request status.', { query, binds });

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error constructing query for updating user access request status: ${err.message}`,
            { requestId, status }
        );
        throw err;
    }
}
//Done
async function createUserAccessRequestQuery(requestId, userId) {
    try {
        const query = `
            INSERT INTO "IFP_USER_ACCESS_REQUEST" ("REQUEST_ID", "USER_ID", "STATUS")
            VALUES (:requestId, :userId, 'PENDING')
        `;
        const binds = {
            requestId: requestId,
            userId: userId
        };

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error generating the insert query for access request: ${err.message}`,
            { requestId, userId }
        );
        throw err;
    }
}
//Done
async function createUserAccessQuery(
    requestId,
    accessId,
    userId,
    domain,
    accessLevel,
    accessOperation
) {
    try {
        const query = `
            INSERT INTO "IFP_USER_ACCESS"
                ("REQUEST_ID","ACCESS_ID","USER_ID","DOMAIN","ACCESS_LEVEL","ACCESS_OPERATION","CREATED_AT","UPDATED_AT")
            VALUES
                (:requestId, :accessId, :userId, :domain, :accessLevel, :accessOperation, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        const binds = {
            requestId: requestId,
            accessId: accessId,
            userId: userId,
            domain: domain,
            accessLevel: accessLevel,
            accessOperation: accessOperation
        };

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error generating query for createUserAccess: ${err.message}`,
            { requestId, accessId, userId, domain, accessLevel, accessOperation }
        );
        throw err;
    }
}

async function createUserRoleQuery(userId) {
    try {
        let object_id = uuid.v4();
        let role = 2;
        const query = `
            INSERT INTO "IFP_FLOW_USER_ROLES"
                ("object_id", user, role, createdAt, updatedAt)
            VALUES
                ( :object_id, :userId, :role, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        const binds = {
            userId: userId,
            object_id: object_id,
            role: role
        };

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error generating query for createUserRole: ${err.message}`,
            { userId }
        );
        throw err;
    }
}
//Done
async function createUserAccessApprovalQuery(
    id,
    userAccessId,
    status,
    nextApprovalLevel,
    approverId,
    reason
) {
    try {
        const query = `
            INSERT INTO "IFP_USER_ACCESS_APPROVALS"
                ("ID","USER_ACCESS_ID","STATUS","APPROVAL_LEVEL","APPROVER_ID","REASON","CREATED_AT","UPDATED_AT")
            VALUES
                (:id, :userAccessId, :status, :nextApprovalLevel, :approverId, :reason, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        const binds = {
            id: id ?? null,
            userAccessId: userAccessId ?? null,
            status: status ?? null,
            nextApprovalLevel: nextApprovalLevel ?? null,
            approverId: approverId ?? null,
            reason: reason ?? null
        };

        return { query, binds };
    } catch (err) {
        logger.error(`Error generating query for createUserAccessApproval: ${err.message}`, {
            id, userAccessId, status, nextApprovalLevel, approverId, reason
        });
        throw err;
    }
}
//Done
/**
 * Get the final approved / revoked access for a given
 * request id. Ensures that only approved and revoked
 * access are assigned / removed.
 * @param {*} accessIds 
 * @returns 
 */
async function getFinalAccessQuery(requestId) {
    try {
        logger.info('Constructing query to fetch final access by requestId.', { requestId });

        const query = `
            SELECT iua.*
            FROM "IFP_USER_ACCESS" iua
            INNER JOIN "IFP_USER_ACCESS_APPROVALS" iuaa
                ON iua."ACCESS_ID" = iuaa."USER_ACCESS_ID"
            INNER JOIN "IFP_USER_ACCESS" iua2
                ON iuaa."USER_ACCESS_ID" = iua2."ACCESS_ID"
            WHERE iua2."REQUEST_ID" = :requestId
              AND iuaa."STATUS" IN ('APPROVED', 'REVOKED')
        `;

        const binds = { requestId: requestId ?? null }; // ensure undefined → null

        logger.info('Query constructed for final access fetch.', { query, binds });
        return { query, binds };
    } catch (err) {
        logger.error(`Error constructing query for final access fetch: ${err.message}`, { requestId });
        throw err;
    }
}
//Done
async function getExistingUsersQuery() {
    try {
        logger.info('Constructing query to fetch existing internal users.');

        const query = `
            SELECT
                iui."ID",
                iui."NAME",
                iui."EMAIL",
                string_agg(iui."AD_GROUP", ', ' ORDER BY iui."AD_GROUP") AS "AD_GROUPS"
            FROM
                "IFP_USER_INFO" iui
            WHERE
                iui."USER_TYPE" = 'INTERNAL'
            AND 
                iui."EMAIL" = '<EMAIL>'

            GROUP BY
                iui."ID",
                iui."NAME",
                iui."EMAIL"
        `;

        logger.info('Query constructed for existing internal users.', { query });
        return { query };
    } catch (err) {
        logger.error(`Error constructing query for existing internal users: ${err.message}`);
        throw err;
    }
}
//Done
async function createUserQuery(
  userId,
  name,
  email,
  phoneNumber,
  role,
  entityId,
  designation,
  existingUserLinkStatus,
  activationFlag,
  ndaStatus
) {
  try {
    logger.info('Constructing createUserQuery.', { userId, email });

    const binds = {
      userId: userId ?? null,
      name: name ?? null,
      email: email ?? null,
      phoneNumber: phoneNumber ?? null,
      role: role ?? null,
      entityId: entityId ?? null,
      designation: designation ?? null,
      existingUserLinkStatus: existingUserLinkStatus ?? null,
      activationFlag: activationFlag ?? null,
      ndaStatus: ndaStatus ?? null
    };

    const query = `
      INSERT INTO "IFP_FLOW_USERS_V2"
        ("ID","NAME","EMAIL","PHONE_NUMBER","DESIGNATION","ROLE","ENTITY_ID","STATUS","ACTIVATION_FLAG","CREATED_AT","UPDATED_AT","EXISTING_USER","IS_NDA_ACCEPTED")
      SELECT
        :userId, :name, :email, :phoneNumber, :designation, :role, :entityId,
        'REGISTERED', :activationFlag, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        :existingUserLinkStatus, :ndaStatus
      WHERE NOT EXISTS (
        SELECT 1 FROM "IFP_FLOW_USERS_V2" WHERE "ID" = :userId
      )
    `;

    logger.debug('createUserQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error with createUserQuery: ${err.message}`, {
      userId, email, entityId
    });
    throw err;
  }
}
//Done
/**
 * Update user
 * @param {string} updateByField Field which record to be updated.
 * Either ID or EMAIL.
 * @param {string} identifier Value for the updateByField
 * @param {object} updateFields Object of key value pairs where key
 * is the column name and value the value to be updated
 * @returns 
 */
async function updateUserDataQuery(updateByField, identifier, updateFields) {
  try {
    logger.info('Constructing updateUserDataQuery.', { updateByField, identifier });

    // Helper to preserve case sensitivity on identifiers
    const quoteIdent = (name) =>
      typeof name === 'string' && name.startsWith('"') && name.endsWith('"')
        ? name
        : `"${name}"`;

    const binds = { identifier: identifier ?? null };
    const setClauses = [];

    Object.entries(updateFields || {}).forEach(([column, value]) => {
      if (value !== undefined) { // allow null, skip only undefined
        const quotedCol = quoteIdent(column);
        const bindKey = column; // keep :column style for binds
        setClauses.push(`${quotedCol} = :${bindKey}`);
        binds[bindKey] = value ?? null; // ensure undefined → null if ever passed
      }
    });

    if (setClauses.length === 0) {
      throw new Error('No columns provided to update.');
    }

    const setQuery = setClauses.join(', ');

    // Build WHERE by chosen field
    let whereField = '"ID"';
    if (updateByField === 'EMAIL') {
      whereField = '"EMAIL"';
    } else if (updateByField === 'MOBILE') {
      whereField = '"PHONE_NUMBER"';
    }

    const query = `
      UPDATE "IFP_FLOW_USERS_V2"
      SET ${setQuery}
      WHERE ${whereField} = :identifier
    `;

    logger.debug('updateUserDataQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error with updateUserDataQuery: ${err.message}`, {
      updateByField, identifier, updateFields
    });
    throw err;
  }
}
//Done
async function incrementMobileSyncFailureFlagQuery(userId) {
  try {
    logger.info('Constructing query to increment mobile sync failed attempts.', { userId });

    const binds = { userId: userId ?? null };

    const query = `
      UPDATE "IFP_FLOW_USERS_V2"
      SET "MOBILE_SYNC_FAILED_ATTEMPTS" = COALESCE("MOBILE_SYNC_FAILED_ATTEMPTS", 0) + 1
      WHERE "ID" = :userId
    `;

    logger.info('Query constructed for incrementing mobile sync failure flag.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(
      `Error constructing query to increment mobile sync failure flag: ${err.message}`,
      { userId }
    );
    throw err;
  }
}
//Done
async function getPendingMobileSyncLinkedUsersQuery() {
  try {
    logger.info('Constructing query to fetch pending mobile sync linked users.');

    const binds = {};
    const query = `
      SELECT
        *
      FROM
        "IFP_FLOW_USERS_V2" AS ifuv
      WHERE
        ifuv."EXISTING_USER" = 'EXISTING_LINKED'
        AND ifuv."MOBILE_SYNC_STATUS" IN ('PENDING', 'NA_ONLY_WELCOME_EMAIL')
    `;

    logger.info('Query constructed for pending mobile sync linked users.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error constructing query for pending mobile sync linked users: ${err.message}`);
    throw err;
  }
}
//Done
async function deleteUserAccessRequestQuery(requestId) {
  try {
    logger.info('Constructing deleteUserAccessRequestQuery.', { requestId });

    const binds = { requestId: requestId ?? null };

    const query = `
      DELETE FROM "IFP_USER_ACCESS_REQUEST"
      WHERE "REQUEST_ID" = :requestId
    `;

    logger.info('deleteUserAccessRequestQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error constructing deleteUserAccessRequestQuery: ${err.message}`, { requestId });
    throw err;
  }
}
//Done
async function deleteUserAccessQuery(userAccessIds) {
  try {
    logger.info('Constructing deleteUserAccessQuery.', {
      count: Array.isArray(userAccessIds) ? userAccessIds.length : 0
    });

    if (!Array.isArray(userAccessIds) || userAccessIds.length === 0) {
      throw new Error('userAccessIds must be a non-empty array');
    }

    const binds = {};
    const placeholders = userAccessIds.map((_, index) => {
      const key = `userAccessId${index}`;
      binds[key] = userAccessIds[index] ?? null; // ensure undefined → null
      return `:${key}`;
    });

    const query = `
      DELETE FROM "IFP_USER_ACCESS"
      WHERE "ACCESS_ID" IN (${placeholders.join(', ')})
    `;

    logger.debug('deleteUserAccessQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error constructing deleteUserAccessQuery: ${err.message}`, { userAccessIds });
    throw err;
  }
}
//Done
async function deleteUserAccessApprovalsQuery(accessIds) {
  try {
    logger.info('Constructing deleteUserAccessApprovalsQuery.', {
      count: Array.isArray(accessIds) ? accessIds.length : 0
    });

    if (!Array.isArray(accessIds) || accessIds.length === 0) {
      throw new Error('accessIds must be a non-empty array');
    }

    const binds = {};
    const placeholders = accessIds.map((id, index) => {
      const key = `accessId${index}`;
      binds[key] = id ?? null; // ensure undefined → null
      return `:${key}`;
    });

    const query = `
      DELETE FROM "IFP_USER_ACCESS_APPROVALS"
      WHERE "USER_ACCESS_ID" IN (${placeholders.join(', ')})
    `;

    logger.debug('deleteUserAccessApprovalsQuery constructed.', { query, binds });
    return { query, binds };
  } catch (err) {
    logger.error(`Error constructing deleteUserAccessApprovalsQuery: ${err.message}`, { accessIds });
    throw err;
  }
}

module.exports = { 
    getADUserUpdateQuery,
    truncateADUserListQuery,
    getUserDataQuery,
    setUserActivationStatusQuery,
    getDisseminationAccessPolicyQuery,
    getUserAccessRequestsQuery,
    getUserAccessLevelsQuery,
    updateUserAccessRequestEntraIdStatusQuery,
    createUserAccessRequestQuery,
    createUserAccessQuery,
    createUserRoleQuery,
    createUserAccessApprovalQuery,
    updateUserAccessRequestStatusQuery,
    getExistingUsersQuery,
    createUserQuery,
    getFinalAccessQuery,
    updateUserDataQuery,
    incrementMobileSyncFailureFlagQuery,
    getPendingMobileSyncLinkedUsersQuery,
    deleteUserAccessQuery,
    deleteUserAccessApprovalsQuery,
    deleteUserAccessRequestQuery
}