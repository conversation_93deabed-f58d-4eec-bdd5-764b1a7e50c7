const logger = require('../services/logger');

async function getADUserUpdateQuery(data) {
    try {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('Data must be a non-empty array');
        }

        const tableName = 'IFP_USER_INFO';
        const columns = ['ID', 'NAME', 'EMAIL', 'ENTITY', 'DEPARTMENT', 'AD_GROUP','DOMAIN','CLASSIFICATION','USER_TYPE', 'USER_CREATED_DT', 'ENC_EMAIL'];

        let binds = {};
        let valueColumnMap = {
            "ID": "id",
            "NAME": "name",
            "EMAIL": "email",
            "DEPARTMENT": "department",
            "USER_CREATED_DT": "createdDateTime",
            "ENC_EMAIL": "encEmail"
        };
        let query = `INSERT ALL `;
        let bindCount = 0;
        data.forEach(groupData=>{
            groupData.members.forEach((row, index) => {
                query += `INTO ${tableName} (${columns.join(', ')}) VALUES (`;
    
                columns.forEach((col, colIndex) => {
                    let bindKey = `bind${bindCount}`;
                    if (col === "ENTITY") {
                        if (row['email']){
                            entity = row["email"].split('@')[1].split('.')[0]
                            if (entity)
                                entity = entity.toUpperCase()
                        }
                        binds[bindKey] = entity?entity:"";
                    } else if (col === "AD_GROUP") {
                        binds[bindKey] = groupData.name;
                    } else if (col === "DOMAIN") {
                        let splitGroup = groupData.name.split('__')
                        binds[bindKey] = splitGroup?splitGroup[1]:'';
                    } else if (col === "CLASSIFICATION") {
                        let classification = groupData.name.split('__').pop()
                        binds[bindKey] = ["OPEN","SENSITIVE","CONFIDENTIAL","SECRET"].includes(classification)?classification:null;
                    } else if (col === "USER_TYPE") {
                        binds[bindKey] = row["email"] && !row["email"].includes("scad.gov.ae")?"EXTERNAL":"INTERNAL"
                    } else if (col === "USER_CREATED_DT") {
                        binds[bindKey] = new Date(row[valueColumnMap[col]]);
                    } else {
                        binds[bindKey] = row[valueColumnMap[col]];
                    }
    
                    query += `:${bindKey}`;
                    if (colIndex < columns.length - 1) {
                        query += ', ';
                    }
                    bindCount++;
                });
    
                query += ') ';
            });
        })

        query += 'SELECT 1 FROM DUAL';

        return { query, binds };
    } catch (err) {
        console.error(`Error in getADUserUpdateQuery: ${err}`);
        throw err;
    }
}

async function truncateADUserListQuery(){
    let query = 'TRUNCATE TABLE IFP_USER_INFO';

    return query;
}

async function getUserDataQuery(filters) {
    try {
      let query = "SELECT * FROM IFP_FLOW_USERS_V2";
      const binds = {};
      const conditions = [];
  
      Object.keys(filters).forEach((field) => {
        const values = filters[field];
        if (Array.isArray(values)) {
          const placeholders = values
            .map((_, index) => `:${field}${index}`)
            .join(", ");
          conditions.push(`${field} IN (${placeholders})`);
          values.forEach((value, index) => {
            binds[`${field}${index}`] = value;
          });
        } else {
          if (values == null) {
            conditions.push(`${field} IS NULL`)
          } else {
            conditions.push(`${field} = :${field}`);
            binds[field] = values;
          }
        }
      });
  
      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
  
      return { query, binds };
    } catch (err) {
      console.error(`Error in getUserDataQuery: ${err}`);
      throw err;
    }
  }

  async function setUserActivationStatusQuery(userId, status) {
    try {
      const query = `
            UPDATE IFP_FLOW_USERS_V2 SET
            ACTIVATION_FLAG = :status, UPDATED_AT = SYSDATE
            WHERE EMAIL = :userId
          `;
      const binds = {
        userId,
        status,
      };
  
      return { query: query, binds: binds };
    } catch (err) {
      console.error(err);
      throw err;
    }
  }

  
  async function getDisseminationAccessPolicyQuery(entityId) {
    try {
        const query = `
            SELECT DOMAIN, CLASSIFICATION FROM IFP_DISS_ACCESS_POLICY WHERE ENTITY_ID = :entityId
        `;
        const binds = {
            entityId,
        };

        logger.info('Constructed dissemination access policy query successfully.', {
            entityId,
            query,
        });

        return { query, binds };
    } catch (err) {
        logger.error(
            `Error constructing dissemination access policy query: ${err.message}`,
            { entityId }
        );
        throw err;
    }
}

async function getUserAccessRequestsQuery(filters) {
    try {
      logger.info('Constructing query to fetch user access requests.', { filters });
  
      let query = "SELECT * FROM IFP_USER_ACCESS_REQUEST";
      const binds = {};
      const conditions = [];
  
      Object.keys(filters).forEach((field) => {
        const values = filters[field];
        if (Array.isArray(values)) {
          const placeholders = values.map((_, index) => `:${field}${index}`).join(", ");
          conditions.push(`${field} IN (${placeholders})`);
          values.forEach((value, index) => {
            binds[`${field}${index}`] = value;
          });
        } else {
          conditions.push(`${field} = :${field}`);
          binds[field] = values;
        }
      });
  
      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
  
      logger.debug('Query constructed for fetching user access requests.', { query, binds });
  
      return { query, binds };
    } catch (err) {
      logger.error(
        `Error constructing query for user access requests: ${err.message}`,
        { filters }
      );
      throw err;
    }
  }

  async function getUserAccessLevelsQuery(filters) {
    try {
      logger.info('Constructing query to fetch user access levels.', { filters });
  
      let query = "SELECT * FROM IFP_USER_ACCESS";
      const binds = {};
      const conditions = [];
  
      Object.keys(filters).forEach((field) => {
        const values = filters[field];
        if (Array.isArray(values)) {
          const placeholders = values
            .map((_, index) => `:${field}${index}`)
            .join(", ");
          conditions.push(`${field} IN (${placeholders})`);
          values.forEach((value, index) => {
            binds[`${field}${index}`] = value;
          });
        } else {
          conditions.push(`${field} = :${field}`);
          binds[field] = values;
        }
      });
  
      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
  
      logger.debug('Query constructed for fetching user access levels.', { query, binds });
  
      return { query, binds };
    } catch (err) {
      logger.error(
        `Error constructing query for user access levels: ${err.message}`,
        { filters }
      );
      throw err;
    }
  }
  
  async function updateUserAccessRequestEntraIdStatusQuery(requestId, status) {
    try {
      logger.info('Constructing query to update user access request Entra ID status.', { requestId, status });
  
      const query = `
        UPDATE IFP_USER_ACCESS_REQUEST 
        SET ENTRA_ID_ASSIGN_STATUS = :status, ENTRA_ID_ASSIGN_DT = SYSDATE 
        WHERE REQUEST_ID = :requestId
      `;
      const binds = {
        requestId,
        status
      };
  
      logger.debug('Query constructed for updating user access request status.', { query, binds });
  
      return { query, binds };
    } catch (err) {
      logger.error(
        `Error constructing query for updating user access request status: ${err.message}`,
        { requestId, status }
      );
      throw err;
    }
  }

  async function updateUserAccessRequestStatusQuery(requestId, status) {
    try {
      logger.info('Constructing query to update user access request status.', { requestId, status });
  
      const query = `
        UPDATE IFP_USER_ACCESS_REQUEST 
        SET STATUS = :status
        WHERE REQUEST_ID = :requestId
      `;
      const binds = {
        requestId,
        status
      };
  
      logger.debug('Query constructed for updating user access request status.', { query, binds });
  
      return { query, binds };
    } catch (err) {
      logger.error(
        `Error constructing query for updating user access request status: ${err.message}`,
        { requestId, status }
      );
      throw err;
    }
  }
  
  async function createUserAccessRequestQuery(
    requestId,
    userId
  ) {
    try {
      const query = `
            INSERT INTO IFP_USER_ACCESS_REQUEST (REQUEST_ID,USER_ID, STATUS)
            VALUES (:requestId,:userId,'PENDING')
          `;
      const binds = {
        requestId,
        userId
      };
  
      return { query: query, binds: binds };
    } catch (err) {
      logger.error(
        `Error generating the insert query for access request ${err} `
      );
      throw err;
    }
  }

  async function createUserAccessQuery(
    requestId,
    accessId,
    userId,
    domain,
    accessLevel,
    accessOperation
  ) {
    try {
      const query = `
            INSERT INTO IFP_USER_ACCESS (REQUEST_ID,ACCESS_ID, USER_ID, DOMAIN, ACCESS_LEVEL,ACCESS_OPERATION, CREATED_AT, UPDATED_AT)
            VALUES (:requestId,:accessId,:userId, :domain, :accessLevel,:accessOperation, SYSDATE, SYSDATE)
          `;
      const binds = {
        requestId,
        accessId,
        userId,
        domain,
        accessLevel,
        accessOperation
      };
  
      return { query: query, binds: binds };
    } catch (err) {
      logger.error(
        `Error generating query for createUserAccess ${err} `
      );
      throw err;
    }
  }
  
  async function createUserAccessApprovalQuery(
    id,
    userAccessId,
    status,
    nextApprovalLevel,
    approverId,
    reason
  ) {
    try {
      const query = `
            INSERT INTO IFP_USER_ACCESS_APPROVALS (ID, USER_ACCESS_ID, STATUS, APPROVAL_LEVEL, APPROVER_ID, REASON, CREATED_AT, UPDATED_AT)
              VALUES (:id, :userAccessId, :status, :nextApprovalLevel, :approverId, :reason, SYSDATE, SYSDATE)
          `;
      const binds = {
        id,
        userAccessId,
        status,
        nextApprovalLevel,
        approverId,
        reason,
      };
  
      return { query: query, binds: binds };
    } catch (err) {
      logger.error(
        `Error generating query for createUserAccessApproval ${err} `
      );
      throw err;
    }
  }

/**
 * Get the final approved / revoked access for a given
 * request id. Ensures that only approved and revoked
 * access are assigned / removed.
 * @param {*} accessIds 
 * @returns 
 */
async function getFinalAccessQuery(requestId) {
  const query = `
    SELECT iua.*
    FROM IFP_USER_ACCESS iua
    INNER JOIN IFP_USER_ACCESS_APPROVALS iuaa ON iua.ACCESS_ID = iuaa.USER_ACCESS_ID
    INNER JOIN IFP_USER_ACCESS iua2 ON iuaa.USER_ACCESS_ID = iua2.ACCESS_ID
    WHERE iua2.REQUEST_ID = :requestId
      AND iuaa.STATUS IN ('APPROVED', 'REVOKED')
  `;
  const binds = { requestId };
  return { query, binds }
}

function getExistingUsersQuery() {
  const query = `
    SELECT
      iui.ID,
      iui.NAME,
      iui.EMAIL,
      LISTAGG(AD_GROUP, ', ') WITHIN GROUP (ORDER BY AD_GROUP) AS AD_GROUPS
    FROM
      IFP_USER_INFO iui
    WHERE
      USER_TYPE = 'INTERNAL'
    GROUP BY
      ID,
      NAME,
      EMAIL
  `;
  return { query }
}

async function createUserQuery(
  userId,
  name,
  email,
  phoneNumber,
  role,
  entityId,
  designation,
  existingUserLinkStatus,
  activationFlag,
  ndaStatus
) {
  try {
    const binds = {
      userId: userId,
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      role: role,
      entityId: entityId,
      designation: designation,
      existingUserLinkStatus: existingUserLinkStatus,
      activationFlag: activationFlag,
      ndaStatus: ndaStatus
    };
    const query = `
      INSERT INTO IFP_FLOW_USERS_V2 (ID, NAME, EMAIL, PHONE_NUMBER,DESIGNATION, ROLE, ENTITY_ID, STATUS, ACTIVATION_FLAG, CREATED_AT, UPDATED_AT, EXISTING_USER, IS_NDA_ACCEPTED)
      SELECT :userId, :name, :email, :phoneNumber,:designation, :role, :entityId, 'REGISTERED', :activationFlag, SYSDATE, SYSDATE, :existingUserLinkStatus, :ndaStatus
      FROM DUAL
      WHERE NOT EXISTS (SELECT 1 FROM IFP_FLOW_USERS_V2 WHERE ID = :userId)
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `Error with createUserQuery with error ${err} `
    );
    throw err;
  }
}

/**
 * Update user
 * @param {string} updateByField Field which record to be updated.
 * Either ID or EMAIL.
 * @param {string} identifier Value for the updateByField
 * @param {object} updateFields Object of key value pairs where key
 * is the column name and value the value to be updated
 * @returns 
 */
function updateUserDataQuery(updateByField, identifier, updateFields) {
  try {
    let binds = { identifier };

    let setClauses = [];
    for (let [column, value] of Object.entries(updateFields)) {
      if (value !== undefined) {
        setClauses.push(`${column} = :${column}`);
        binds[column] = value;
      }
    }

    let setQuery = setClauses.join(", ");

    let whereClause = "WHERE ID = :identifier";
    if (updateByField == "EMAIL") {
      whereClause = "WHERE EMAIL = :identifier";
    } else if (updateByField == "MOBILE") {
      whereClause = "WHERE PHONE_NUMBER = :identifier"
    }
    let query = `UPDATE IFP_FLOW_USERS_V2 SET ${setQuery} ${whereClause}`;

    return { query, binds };
  } catch (err) {
    log.error(`Error with updateUserDataQuery with error ${err}`);
    throw err;
  }
}

function incrementMobileSyncFailureFlagQuery(userId) {
  const binds = { userId };
  const query = `
    UPDATE IFP_FLOW_USERS_V2
      SET MOBILE_SYNC_FAILED_ATTEMPTS = MOBILE_SYNC_FAILED_ATTEMPTS + 1
    WHERE ID = :userId
  `;
  return { query, binds }
}

function getPendingMobileSyncLinkedUsersQuery() {
  const binds = {};
  const query = `
    SELECT
      *
    FROM
      IFP_FLOW_USERS_V2 ifuv
    WHERE
      ifuv.EXISTING_USER = 'EXISTING_LINKED'
      AND (ifuv.MOBILE_SYNC_STATUS = 'PENDING'
        OR ifuv.MOBILE_SYNC_STATUS = 'NA_ONLY_WELCOME_EMAIL')
  `;
  return { query, binds }
}

module.exports = { 
    getADUserUpdateQuery,
    truncateADUserListQuery,
    getUserDataQuery,
    setUserActivationStatusQuery,
    getDisseminationAccessPolicyQuery,
    getUserAccessRequestsQuery,
    getUserAccessLevelsQuery,
    updateUserAccessRequestEntraIdStatusQuery,
    createUserAccessRequestQuery,
    createUserAccessQuery,
    createUserAccessApprovalQuery,
    updateUserAccessRequestStatusQuery,
    getExistingUsersQuery,
    createUserQuery,
    getFinalAccessQuery,
    updateUserDataQuery,
    incrementMobileSyncFailureFlagQuery,
    getPendingMobileSyncLinkedUsersQuery,
}