const pgdb = require('../../../database/postgres/platform-db/models').sequelize;
const { bulkAddPolicyRecordQuery } = require("./getQuery.service");
const logger = require("../../../services/logger");

async function bulkAddPolicyRecords(
  entityId,
  entityName,
  domain,
  classifications
) {
  let {query,binds} = await bulkAddPolicyRecordQuery(
    entityId,
    entityName,
    domain,
    classifications
  );
  try {
    await getPGData(query, binds);
  } catch (err) {
    logger.error(`Error adding dissemination policy: ${err.message}`, {
      entityId,
    });
    throw err;
  }
}

async function getPGData(query,binds = {}) {
  try {
    let data = await pgdb.query(query, {replacements: binds });
    return data[0];
  } catch (err) {
    logger.error(`<<<<< Exited services.executeQuery.service.getPGData with error ${err}`);
    logger.error(`Error with getPGData: ${err}`);
    throw err;
  }
}


module.exports = {
  bulkAddPolicyRecords,
};
