const db = require("../../../services/database.service");
const { bulkAddPolicyRecordQuery } = require("./getQuery.service");
const logger = require("../../../services/logger");

async function bulkAddPolicyRecords(
  entityId,
  entityName,
  domain,
  classifications
) {
  let query = bulkAddPolicyRecordQuery(
    entityId,
    entityName,
    domain,
    classifications
  );
  try {
    await db.simpleExecute(query);
  } catch (err) {
    logger.error(`Error adding dissemination policy: ${err.message}`, {
      entityId,
    });
    throw err;
  }
}

module.exports = {
  bulkAddPolicyRecords,
};
