# AD Sync Users Job

This job synchronizes users from Azure Active Directory (by groups) to PostgreSQL tables `AD_SYNC_USERS` and `AD_SYNC_USERS_GROUPS`.

## Overview

The job performs the following operations:
1. Fetches users from Azure AD by specified groups
2. Extracts user information including aliases and group memberships
3. Truncates `AD_SYNC_USERS` table and inserts fetched users
4. Truncates `AD_SYNC_USERS_GROUPS` table and inserts user-group relationships

## Architecture

The implementation follows the Single Responsibility Principle:

- **`services/ad-user-fetcher.service.js`**: Handles all Azure AD API interactions
- **`services/ad-sync-db.service.js`**: Handles all database operations
- **`jobs/ad-sync-users.job.js`**: Orchestrates the sync process
- **`config/adSyncDbConfig.js`**: Database configuration

### Database Schema

The job expects the following tables:

**AD_SYNC_USERS:**
- ID (UUID PRIMARY KEY)
- NAME (VARCHAR(255))
- EMAIL (VARCHAR(255))
- ACCOUNT_ENABLED (BOOLEAN)
- USER_TYPE (VARCHAR(50))
- DEPARTMENT (VARCHAR(255))
- JOB_TITLE (VARCHAR(255))
- CREATED_DATE_TIME (TIMESTAMP)
- ALIASES (JSONB)

**AD_SYNC_USERS_GROUPS:**
- USER_ID (UUID)
- GROUP_ID (UUID)
- PRIMARY KEY (USER_ID, GROUP_ID)

## Usage

### Run the job

```bash
node run.js --job-id ad-sync-users
```

## Features

- **Batch Processing**: Inserts users and relations in batches for optimal performance
- **Error Handling**: Continues processing even if individual groups or users fail
- **Deduplication**: Handles duplicate users across multiple groups
- **Token Caching**: Caches Azure AD access tokens to reduce API calls
- **Rate Limiting**: Includes delays to avoid Azure AD rate limits
- **Comprehensive Logging**: Logs all operations for debugging and monitoring

## Performance Considerations

- Users are inserted in batches of 500
- User-group relations are inserted in batches of 1000
- The job uses connection pooling for database operations
- Azure AD API calls include rate limiting delays

## Error Handling

- If fetching from a group fails, the job continues with other groups
- If processing a user fails, the job continues with other users
- Database errors are logged and propagated
- The job ensures database connections are properly closed even on errors

