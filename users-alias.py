#!/usr/bin/env python3
"""
Python script to find user aliases in Active Directory using Microsoft Graph API
Uses plain HTTP requests without external authentication libraries
"""
 
import requests
import json
import os
import sys
from typing import Dict, List, Optional, Tuple
import time
 
# Environment variables configuration
ENV_VARS = {
    'USER_CLIENT_ID': '32dcfbb1-a089-4749-8c57-ce11485a617b',
    'USER_CLIENT_SECRET': '****************************************',
    'USER_TENANT_ID': '6926239f-3483-4451-8452-48ee3bee086f',
    'USER_GRAPH_ENDPOINT': 'https://graph.microsoft.com'
}
 
class GraphAPIClient:
    """Microsoft Graph API client using plain HTTP requests"""
   
    def __init__(self):
        """Initialize the Graph API client with environment variables"""
        self.client_id = os.getenv('USER_CLIENT_ID', ENV_VARS['USER_CLIENT_ID'])
        self.client_secret = os.getenv('USER_CLIENT_SECRET', ENV_VARS['USER_CLIENT_SECRET'])
        self.tenant_id = os.getenv('USER_TENANT_ID', ENV_VARS['USER_TENANT_ID'])
        self.graph_endpoint = os.getenv('USER_GRAPH_ENDPOINT', ENV_VARS['USER_GRAPH_ENDPOINT'])
       
        # Validate required environment variables
        if not all([self.client_id, self.client_secret, self.tenant_id]):
            print("Error: Missing required environment variables:")
            print("  USER_CLIENT_ID")
            print("  USER_CLIENT_SECRET")
            print("  USER_TENANT_ID")
            sys.exit(1)
           
        self.access_token = None
        self.token_expires_at = 0
       
    def get_access_token(self) -> str:
        """
        Acquire access token using client credentials flow
        Returns: Access token string
        """
        try:
            # Check if we have a valid cached token
            if self.access_token and time.time() < self.token_expires_at:
                return self.access_token
               
            print("Acquiring new access token...")
           
            # Token endpoint URL
            token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
           
            # Request headers
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
           
            # Request body for client credentials flow
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'scope': f'{self.graph_endpoint}/.default',
                'grant_type': 'client_credentials'
            }
           
            # Make token request
            response = requests.post(token_url, headers=headers, data=data)
            response.raise_for_status()
           
            token_data = response.json()
            self.access_token = token_data['access_token']
           
            # Calculate token expiration (subtract 5 minutes for safety)
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = time.time() + expires_in - 300
           
            print("Access token acquired successfully")
            return self.access_token
           
        except requests.exceptions.RequestException as e:
            print(f"Error acquiring access token: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise
        except KeyError as e:
            print(f"Error parsing token response: Missing key {e}")
            raise
   
    def make_graph_request(self, endpoint: str, params: Dict = None) -> Dict:
        """
        Make authenticated request to Microsoft Graph API
        Args:
            endpoint: Graph API endpoint (without base URL)
            params: Query parameters
        Returns:
            JSON response data
        """
        try:
            # Get access token
            token = self.get_access_token()
           
            # Prepare headers
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
           
            # Build full URL
            url = f"{self.graph_endpoint}/v1.0{endpoint}"
           
            # Make request
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
           
            return response.json()
           
        except requests.exceptions.RequestException as e:
            print(f"Graph API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise
   
    def generate_email_variations(self, email: str) -> List[str]:
        """
        Generate various email format variations for alias checking
        Args:
            email: Original email address
        Returns:
            List of email variations
        """
        variations = set()
       
        # Add original email (lowercase)
        variations.add(email.lower())
       
        # Parse email parts
        try:
            local_part, domain = email.lower().split('@')
        except ValueError:
            return [email.lower()]
       
        # Common variations for SCAD domain
        if 'scad.gov.ae' in domain:
            variations.add(f"{local_part}@scad.gov.ae")
            variations.add(f"{local_part}@scad.ae")
            variations.add(f"{local_part}@adm.scad.gov.ae")
       
        # Common name format variations
        if '.' in local_part:
            parts = local_part.split('.')
            if len(parts) >= 2:
                first_name, last_name = parts[0], parts[1]
               
                # Various combinations
                variations.add(f"{first_name}.{last_name}@{domain}")
                variations.add(f"{last_name}.{first_name}@{domain}")
                variations.add(f"{first_name}{last_name}@{domain}")
                variations.add(f"{last_name}{first_name}@{domain}")
                variations.add(f"{first_name}_{last_name}@{domain}")
                variations.add(f"{last_name}_{first_name}@{domain}")
                variations.add(f"{first_name[0]}{last_name}@{domain}")
                variations.add(f"{first_name}{last_name[0]}@{domain}")
       
        return list(variations)
   
    def check_user_by_mail(self, email: str) -> Optional[Dict]:
        """
        Check user by mail field
        Args:
            email: Email to check
        Returns:
            User object or None
        """
        try:
            endpoint = "/users"
            params = {
                '$filter': f"mail eq '{email}'",
                '$select': 'id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses'
            }
           
            response = self.make_graph_request(endpoint, params)
            users = response.get('value', [])
           
            return users[0] if users else None
           
        except Exception as e:
            print(f"Error checking user by mail {email}: {e}")
            return None
   
    def check_user_by_upn(self, email: str) -> Optional[Dict]:
        """
        Check user by userPrincipalName
        Args:
            email: Email to check as UPN
        Returns:
            User object or None
        """
        try:
            endpoint = "/users"
            params = {
                '$filter': f"userPrincipalName eq '{email}'",
                '$select': 'id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses'
            }
           
            response = self.make_graph_request(endpoint, params)
            users = response.get('value', [])
           
            return users[0] if users else None
           
        except Exception as e:
            print(f"Error checking user by UPN {email}: {e}")
            return None
   
    def check_user_by_proxy_address(self, email: str) -> Optional[Dict]:
        """
        Check user by proxy addresses (alternate email addresses)
        Args:
            email: Email to check in proxy addresses
        Returns:
            User object or None
        """
        try:
            # ProxyAddresses can contain SMTP:email or smtp:email formats
            proxy_formats = [f"smtp:{email}", f"SMTP:{email}"]
           
            for proxy_format in proxy_formats:
                try:
                    endpoint = "/users"
                    params = {
                        '$filter': f"proxyAddresses/any(c:c eq '{proxy_format}')",
                        '$select': 'id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses'
                    }
                   
                    response = self.make_graph_request(endpoint, params)
                    users = response.get('value', [])
                   
                    if users:
                        return users[0]
                       
                except Exception:
                    continue
           
            return None
           
        except Exception as e:
            print(f"Error checking user by proxy address {email}: {e}")
            return None
   
    def find_user_with_aliases(self, user_email: str) -> Dict:
        """
        Find user in Active Directory using various alias formats
        Args:
            user_email: The user's email address
        Returns:
            Result dictionary with found status and user data
        """
        print(f"Searching for user: {user_email}")
       
        try:
            # Generate possible email aliases and formats
            email_variations = self.generate_email_variations(user_email)
            print(f"Generated {len(email_variations)} email variations to check")
           
            # Try each variation
            for i, email in enumerate(email_variations):
                print(f"Trying variation {i + 1}/{len(email_variations)}: {email}")
               
                # Method 1: Direct lookup by mail
                user = self.check_user_by_mail(email)
                if user:
                    print(f"✓ User found by mail: {email} -> {user.get('displayName')}")
                    return {
                        'found': True,
                        'user': user,
                        'matched_email': email,
                        'match_method': 'mail'
                    }
               
                # Method 2: Lookup by userPrincipalName
                user = self.check_user_by_upn(email)
                if user:
                    print(f"✓ User found by UPN: {email} -> {user.get('displayName')}")
                    return {
                        'found': True,
                        'user': user,
                        'matched_email': email,
                        'match_method': 'userPrincipalName'
                    }
               
                # Method 3: Search by proxyAddresses
                user = self.check_user_by_proxy_address(email)
                if user:
                    print(f"✓ User found by proxy address: {email} -> {user.get('displayName')}")
                    return {
                        'found': True,
                        'user': user,
                        'matched_email': email,
                        'match_method': 'proxyAddresses'
                    }
               
                # Small delay between API calls to avoid rate limiting
                time.sleep(0.1)
           
            print(f"✗ User not found with any alias variation for: {user_email}")
            return {
                'found': False,
                'user': None,
                'checked_variations': email_variations
            }
           
        except Exception as e:
            print(f"Error in find_user_with_aliases for {user_email}: {e}")
            return {
                'found': False,
                'error': str(e),
                'user_email': user_email
            }
   
    def get_user_details(self, user_id: str) -> Optional[Dict]:
        """
        Get detailed user information by user ID
        Args:
            user_id: User ID from Azure AD
        Returns:
            User details or None
        """
        try:
            endpoint = f"/users/{user_id}"
            params = {
                '$select': 'id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,phoneNumber'
            }

            response = self.make_graph_request(endpoint, params)
            return response

        except Exception as e:
            print(f"Error getting user details for {user_id}: {e}")
            return None

    def get_all_users(self, page_size: int = 999) -> List[Dict]:
        """
        Fetch all users from Azure AD with pagination support
        Args:
            page_size: Number of users to fetch per page (max 999)
        Returns:
            List of all users with their details
        """
        all_users = []
        next_link = None
        page_count = 0

        try:
            print("Starting to fetch all users from Azure AD...")

            while True:
                page_count += 1
                print(f"Fetching page {page_count}...")

                if next_link:
                    # Use the next link for pagination
                    response = self._make_paginated_request(next_link)
                else:
                    # First request
                    endpoint = "/users"
                    params = {
                        '$select': 'id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,department,jobTitle,officeLocation,phoneNumber,createdDateTime,lastSignInDateTime',
                        '$top': page_size,
                        '$orderby': 'displayName'
                    }
                    response = self.make_graph_request(endpoint, params)

                # Extract users from response
                users = response.get('value', [])
                all_users.extend(users)

                print(f"  Retrieved {len(users)} users (Total so far: {len(all_users)})")

                # Check for next page
                next_link = response.get('@odata.nextLink')
                if not next_link:
                    break

                # Small delay to avoid rate limiting
                time.sleep(0.5)

            print(f"\n✓ Successfully retrieved {len(all_users)} total users from Azure AD")
            return all_users

        except Exception as e:
            print(f"Error fetching all users: {e}")
            return all_users  # Return what we have so far

    def _make_paginated_request(self, next_link: str) -> Dict:
        """
        Make a request using the @odata.nextLink for pagination
        Args:
            next_link: The full URL for the next page
        Returns:
            JSON response data
        """
        try:
            # Get access token
            token = self.get_access_token()

            # Prepare headers
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            # Make request using the full next_link URL
            response = requests.get(next_link, headers=headers)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"Paginated request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise

    def extract_user_aliases(self, user: Dict) -> Dict:
        """
        Extract all email aliases for a user
        Args:
            user: User object from Azure AD
        Returns:
            Dictionary with user info and all email aliases
        """
        aliases = set()

        # Add primary email
        if user.get('mail'):
            aliases.add(user['mail'].lower())

        # Add UPN
        if user.get('userPrincipalName'):
            aliases.add(user['userPrincipalName'].lower())

        # Extract from proxy addresses
        proxy_addresses = user.get('proxyAddresses', [])
        for proxy in proxy_addresses:
            if proxy.startswith('smtp:') or proxy.startswith('SMTP:'):
                email = proxy[5:]  # Remove 'smtp:' or 'SMTP:' prefix
                aliases.add(email.lower())

        return {
            'id': user.get('id'),
            'displayName': user.get('displayName'),
            'primaryEmail': user.get('mail'),
            'userPrincipalName': user.get('userPrincipalName'),
            'accountEnabled': user.get('accountEnabled'),
            'department': user.get('department'),
            'jobTitle': user.get('jobTitle'),
            'officeLocation': user.get('officeLocation'),
            'phoneNumber': user.get('phoneNumber'),
            'createdDateTime': user.get('createdDateTime'),
            'lastSignInDateTime': user.get('lastSignInDateTime'),
            'aliases': sorted(list(aliases)),
            'aliasCount': len(aliases),
            'proxyAddresses': proxy_addresses
        }

    def get_all_users_with_aliases(self, output_file: str = None) -> List[Dict]:
        """
        Fetch all users and extract their email aliases
        Args:
            output_file: Optional file path to save results as JSON
        Returns:
            List of users with their aliases
        """
        print("Fetching all users with their email aliases...")

        # Get all users
        all_users = self.get_all_users()

        if not all_users:
            print("No users found or error occurred")
            return []

        # Process each user to extract aliases
        users_with_aliases = []
        print(f"\nProcessing {len(all_users)} users to extract aliases...")

        for i, user in enumerate(all_users, 1):
            if i % 100 == 0:
                print(f"  Processed {i}/{len(all_users)} users...")

            user_data = self.extract_user_aliases(user)
            users_with_aliases.append(user_data)

        print(f"✓ Processed all {len(users_with_aliases)} users")

        # Save to file if requested
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(users_with_aliases, f, indent=2, ensure_ascii=False)
                print(f"✓ Results saved to {output_file}")
            except Exception as e:
                print(f"Error saving to file: {e}")

        return users_with_aliases

    def print_user_summary(self, users_with_aliases: List[Dict]):
        """
        Print a summary of users and their aliases
        Args:
            users_with_aliases: List of users with alias data
        """
        if not users_with_aliases:
            print("No users to summarize")
            return

        print(f"\n{'='*80}")
        print(f"USER SUMMARY")
        print(f"{'='*80}")
        print(f"Total Users: {len(users_with_aliases)}")

        # Count statistics
        enabled_users = sum(1 for u in users_with_aliases if u.get('accountEnabled'))
        users_with_multiple_aliases = sum(1 for u in users_with_aliases if u.get('aliasCount', 0) > 1)
        total_aliases = sum(u.get('aliasCount', 0) for u in users_with_aliases)

        print(f"Enabled Users: {enabled_users}")
        print(f"Disabled Users: {len(users_with_aliases) - enabled_users}")
        print(f"Users with Multiple Aliases: {users_with_multiple_aliases}")
        print(f"Total Email Aliases: {total_aliases}")
        print(f"Average Aliases per User: {total_aliases / len(users_with_aliases):.2f}")

        # Show users with most aliases
        print(f"\n{'='*80}")
        print(f"TOP 10 USERS WITH MOST ALIASES")
        print(f"{'='*80}")

        sorted_users = sorted(users_with_aliases, key=lambda x: x.get('aliasCount', 0), reverse=True)
        for i, user in enumerate(sorted_users[:10], 1):
            print(f"{i:2d}. {user.get('displayName', 'N/A'):<30} ({user.get('aliasCount', 0)} aliases)")
            for alias in user.get('aliases', [])[:3]:  # Show first 3 aliases
                print(f"     - {alias}")
            if user.get('aliasCount', 0) > 3:
                print(f"     ... and {user.get('aliasCount', 0) - 3} more")
            print()

    def search_users_by_alias(self, users_with_aliases: List[Dict], search_email: str) -> List[Dict]:
        """
        Search for users by email alias
        Args:
            users_with_aliases: List of users with alias data
            search_email: Email to search for
        Returns:
            List of matching users
        """
        search_email = search_email.lower().strip()
        matching_users = []

        for user in users_with_aliases:
            aliases = user.get('aliases', [])
            if search_email in aliases:
                matching_users.append(user)

        return matching_users
 
 
def main():
    """Main function to demonstrate user alias lookup"""
   
    # Initialize Graph API client
    client = GraphAPIClient()
   
    # Example usage
    if len(sys.argv) > 1:
        user_email = sys.argv[1]
    else:
        user_email = input("Enter user email to search: ").strip()
   
    if not user_email:
        print("No email provided")
        sys.exit(1)
   
    print(f"\n{'='*60}")
    print(f"SEARCHING FOR USER ALIASES")
    print(f"{'='*60}")
   
    # Search for user with aliases
    result = client.find_user_with_aliases(user_email)
   
    print(f"\n{'='*60}")
    print(f"SEARCH RESULTS")
    print(f"{'='*60}")
   
    if result['found']:
        user = result['user']
        print(f"✓ USER FOUND!")
        print(f"  Display Name: {user.get('displayName', 'N/A')}")
        print(f"  Email: {user.get('mail', 'N/A')}")
        print(f"  UPN: {user.get('userPrincipalName', 'N/A')}")
        print(f"  Account Enabled: {user.get('accountEnabled', 'N/A')}")
        print(f"  Matched Email: {result['matched_email']}")
        print(f"  Match Method: {result['match_method']}")
       
        # Show proxy addresses if available
        proxy_addresses = user.get('proxyAddresses', [])
        if proxy_addresses:
            print(f"  Proxy Addresses:")
            print(f"    {proxy_addresses}")
       
        # Get additional user details
        print(f"\n{'='*60}")
        print(f"ADDITIONAL USER DETAILS")
        print(f"{'='*60}")
       
        user_details = client.get_user_details(user['id'])
        if user_details:
            print(f"  Department: {user_details.get('department', 'N/A')}")
            print(f"  Job Title: {user_details.get('jobTitle', 'N/A')}")
            print(f"  Office Location: {user_details.get('officeLocation', 'N/A')}")
            print(f"  Phone: {user_details.get('phoneNumber', 'N/A')}")
   
    else:
        print(f"✗ USER NOT FOUND")
        if 'error' in result:
            print(f"  Error: {result['error']}")
        else:
            print(f"  Checked {len(result.get('checked_variations', []))} email variations")
            print(f"  Variations checked:")
            for variation in result.get('checked_variations', [])[:10]:  # Show first 10
                print(f"    - {variation}")
            if len(result.get('checked_variations', [])) > 10:
                print(f"    ... and {len(result['checked_variations']) - 10} more")
 
 
if __name__ == "__main__":
    main()