/**
 * Job that syncs <PERSON><PERSON> logs pushed to Redis to Clickhouse table.
 * 
 */

const redis = require("redis");
const { createClient } = require("@clickhouse/client");

const constants = require("../config/constants.json");
const { ensureSeconds } = require("../utils/kpi-logs-sync.utils");
const { getRedisKeys, flushKeys } = require("../services/redis.service");

let redisClient;
let clickhouseClient;

const KPI_LOGS_TABLE = "IFP_KPI_LOGS";

// RUN: node microservice-kpi/kpi.worker.js > /dev/null 2>&1

const initializeClients = async () => {
  if (constants.redis.enabled) {
    redisClient = redis.createClient({
      socket: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
      },
    });

    redisClient.on("error", (error) => {
      throw new Error(`Redis not enabled: ${error}.`);
    });

    await redisClient.connect();
  } else {
    throw new Error("Redis not enabled.");
  }

  clickhouseClient = createClient({
    host: process.env.CLICKHOUSE_HOST,
    username: process.env.CLICKHOUSE_USER,
    password: process.env.CLICKHOUSE_PASSWORD,
    database: process.env.CLICKHOUSE_DATABASE,
    max_open_connections: 50,
    keep_alive: {
      enabled: true,
      socket_ttl: 29000,
      retry_on_expired_socket: true,
    },
  });
};

// Function to process KPI sessions
const processKpiEndSessions = async () => {
  try {
    console.log("IN processKpiEndSessions---");

    // Retrieve all session keys
    const endSessionKeyPatterns = `KPI_${process.env.NODE_ENV}_end_*`;
    const endSessionKeys = await getRedisKeys(endSessionKeyPatterns);
    console.log("endSessionKeys::: ", endSessionKeys);

    let logs = [];
    for (const endSessionKey of endSessionKeys) {
      const endSessionData = await redisClient.get(endSessionKey);
      if (endSessionData) {
        const endSession = JSON.parse(endSessionData);

        const startSessionKey = `KPI_${process.env.NODE_ENV}_start_${endSession.sessionId}`;
        const startSessionData = await redisClient.get(startSessionKey);
        const startSession = JSON.parse(startSessionData);

        if (startSession) {
          // Process the session data
          logs.push({
            sessionId: `${startSession.sessionId}`,
            nodeId: startSession.nodeId,
            userEmail: startSession.userEmail,
            sessionType: startSession.sessionType,
            data1: startSession.data1,
            data2: startSession.data2,
            startTime: ensureSeconds(startSession.time),
            endTime: ensureSeconds(endSession.time),
          });
        }

        // Delete redis keys
        const flushKeyPattern = `KPI_${process.env.NODE_ENV}_*_${endSession.sessionId}`;
        await flushKeys(flushKeyPattern);
      }
    }

    if (logs.length > 0) {
      console.log("end logs: ", logs);

      await clickhouseClient.insert({
        table: KPI_LOGS_TABLE,
        values: logs,
        format: "JSONEachRow",
      });
    }
    console.log("Done----");
  } catch (err) {
    throw new Error(`Error processing KPI session: ${err.message}`);
  }
};

// Function to process Dead KPI sessions
const processKpiDeadSessions = async () => {
  try {
    console.log("\nIN - processKpiDeadSessions ---");

    // Retrieve all session keys
    const startSessionKeyPatterns = `KPI_${process.env.NODE_ENV}_start_*`;
    console.log("startSessionKeyPatterns:: ", startSessionKeyPatterns);

    const startSessionKeys = await getRedisKeys(startSessionKeyPatterns);
    console.log("startSessionKeys::: ", startSessionKeys);

    const deadSessionTimeout = 3; // # 3 seconds
    const sessionMaxTime = 1000 * 60 * 45;
    const currentTime = Date.now();
    const expTime = currentTime - sessionMaxTime;
    console.log("currentTime: ", currentTime, " | expTime: ", expTime);

    let logs = [];
    for (const startSessionKey of startSessionKeys) {
      const startSessionData = await redisClient.get(startSessionKey);
      if (startSessionData) {
        const startSession = JSON.parse(startSessionData);
        const startSessionTime = ensureSeconds(startSession.time);

        if (startSessionTime <= expTime) {
          const endSessionKey = `KPI_${process.env.NODE_ENV}_end_${startSession.sessionId}`;
          const endSessionData = await redisClient.get(endSessionKey);
          const endSession = JSON.parse(endSessionData);

          if (!endSession) {
            // Process the session data
            logs.push({
              sessionId: `${startSession.sessionId}`,
              nodeId: startSession.nodeId,
              userEmail: startSession.userEmail,
              sessionType: startSession.sessionType,
              data1: startSession.data1,
              data2: startSession.data2,
              startTime: startSessionTime,
              endTime: startSessionTime + deadSessionTimeout,
            });

            // Delete redis keys
            const flushKeyPattern = `KPI_${process.env.NODE_ENV}_*_${startSession.sessionId}`;
            await flushKeys(flushKeyPattern);
          }
        }
      }
    }

    if (logs.length > 0) {
      console.log("dead logs: ", logs);

      await clickhouseClient.insert({
        table: KPI_LOGS_TABLE,
        values: logs,
        format: "JSONEachRow",
      });
    }
  } catch (err) {
    throw new Error(`Error processing KPI session: ${err.message}`);
  }
};

const run = async () => {
  await initializeClients();

  try {
    await processKpiEndSessions();
    console.log("KPI end session processing completed.");
  } catch (err) {
    console.error(`Failed to process KPI end sessions: ${err.message}`);
  }

  try {
    await processKpiDeadSessions();
    console.log("KPI dead session processing completed.");
  } catch (err) {
    console.error(`Failed to process KPI dead sessions: ${err.message}`);
  } finally {
    if (redisClient) {
      console.log("Redis closed.");
      await redisClient.quit();
    }
    if (clickhouseClient) {
      console.log("Clickhouse closed.");
      await clickhouseClient.close();
    }
  }
};

module.exports = { run };
