#!/usr/bin/env node

/**
 * Standalone script to check members of a group using group ID from Microsoft Graph API
 * 
 * Usage:
 *   node list-group-members.js --group-id <group-id>
 *   node list-group-members.js --group-id <group-id> --names-only
 *   node list-group-members.js --group-id <group-id> --emails-only
 * 
 * Environment variables required:
 *   USER_CLIENT_ID - Azure AD application client ID
 *   USER_CLIENT_SECRET - Azure AD application client secret
 *   USER_TENANT_ID - Azure AD tenant ID
 *   USER_GRAPH_ENDPOINT - Microsoft Graph API endpoint (usually https://graph.microsoft.com)
 */

const { program } = require('commander');
const msal = require('@azure/msal-node');
const axios = require('axios');
require('dotenv').config();

// Configuration for MSAL
const msalConfig = {
    auth: {
        clientId: process.env.USER_CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.USER_TENANT_ID}`,
        clientSecret: process.env.USER_CLIENT_SECRET,
    }
};

const tokenRequest = {
    scopes: [`${process.env.USER_GRAPH_ENDPOINT}/.default`],
};

// Initialize MSAL client
let cca;
try {
    cca = new msal.ConfidentialClientApplication(msalConfig);
} catch (error) {
    console.error('Failed to initialize MSAL client:', error.message);
    process.exit(1);
}

/**
 * Get authentication token from Microsoft Graph API
 */
async function getAuthToken() {
    try {
        const authData = await cca.acquireTokenByClientCredential(tokenRequest);
        return authData.accessToken;
    } catch (error) {
        console.error('Failed to acquire authentication token:', error.message);
        throw error;
    }
}

/**
 * Get group information by group ID
 */
async function getGroupInfo(groupId) {
    try {
        const authToken = await getAuthToken();
        
        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        console.log(`Fetching group information for ID: ${groupId}`);
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups/${groupId}`,
            options
        );

        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            throw new Error(`Group with ID ${groupId} not found`);
        }
        console.error(`Error fetching group information for ${groupId}:`, error.message);
        throw error;
    }
}

/**
 * Get members of a specific group
 */
async function getGroupMembers(groupId) {
    try {
        console.log(`Fetching members for group ID: ${groupId}`);

        const authToken = await getAuthToken();
        
        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        // Fetch members with pagination support
        let allMembers = [];
        let nextLink = `${process.env.USER_GRAPH_ENDPOINT}/v1.0/groups/${groupId}/members?$top=999&$select=id,displayName,mail,userPrincipalName,userType,accountEnabled,department,jobTitle`;

        while (nextLink) {
            const response = await axios.get(nextLink, options);
            allMembers = allMembers.concat(response.data.value);
            
            // Check if there are more pages
            nextLink = response.data['@odata.nextLink'] || null;
        }

        return allMembers;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            throw new Error(`Group with ID ${groupId} not found`);
        }
        console.error(`Error fetching members for group ${groupId}:`, error.message);
        throw error;
    }
}

/**
 * Format and display group members information
 */
function displayGroupMembers(groupInfo, members, outputType = 'full') {
    console.log('\n' + '='.repeat(80));
    console.log(`GROUP MEMBERS: ${groupInfo.displayName || 'Unknown Group'}`);
    console.log('='.repeat(80));
    console.log(`Group ID: ${groupInfo.id}`);
    console.log(`Group Description: ${groupInfo.description || 'N/A'}`);
    console.log(`Security Enabled: ${groupInfo.securityEnabled || 'N/A'}`);
    console.log(`Mail Enabled: ${groupInfo.mailEnabled || 'N/A'}`);
    console.log(`Created: ${groupInfo.createdDateTime || 'N/A'}`);
    console.log('-'.repeat(80));
    
    if (members.length === 0) {
        console.log('No members found in this group.');
        return;
    }

    console.log(`Total members: ${members.length}\n`);
    
    switch (outputType) {
        case 'names-only':
            console.log('MEMBER NAMES:');
            console.log('-'.repeat(40));
            members.forEach((member, index) => {
                console.log(`${index + 1}. ${member.displayName || 'N/A'}`);
            });
            console.log('-'.repeat(40));
            break;
            
        case 'emails-only':
            console.log('MEMBER EMAILS:');
            console.log('-'.repeat(40));
            members.forEach((member, index) => {
                const email = member.mail || member.userPrincipalName || 'N/A';
                console.log(`${index + 1}. ${email}`);
            });
            console.log('-'.repeat(40));
            break;
            
        case 'full':
        default:
            members.forEach((member, index) => {
                console.log(`${index + 1}. Name: ${member.displayName || 'N/A'}`);
                console.log(`   Email: ${member.mail || member.userPrincipalName || 'N/A'}`);
                console.log(`   User ID: ${member.id}`);
                console.log(`   User Type: ${member.userType || 'N/A'}`);
                console.log(`   Account Enabled: ${member.accountEnabled || 'N/A'}`);
                console.log(`   Department: ${member.department || 'N/A'}`);
                console.log(`   Job Title: ${member.jobTitle || 'N/A'}`);
                console.log('-'.repeat(40));
            });
            break;
    }
}

/**
 * Validate required environment variables
 */
function validateEnvironment() {
    const requiredVars = [
        'USER_CLIENT_ID',
        'USER_CLIENT_SECRET', 
        'USER_TENANT_ID',
        'USER_GRAPH_ENDPOINT'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`  - ${varName}`);
        });
        console.error('\nPlease set these environment variables in your .env file or environment.');
        process.exit(1);
    }
}

/**
 * Main function
 */
async function main() {
    try {
        // Validate environment variables
        validateEnvironment();

        // Parse command line arguments
        program
            .option('--group-id <groupId>', 'Group ID to look up members for')
            .option('--names-only', 'Show only member names (simplified output)')
            .option('--emails-only', 'Show only member emails (simplified output)')
            .parse();

        const options = program.opts();

        if (!options.groupId) {
            console.error('Error: Please provide --group-id');
            console.error('Usage:');
            console.error('  node list-group-members.js --group-id <group-id>');
            console.error('  node list-group-members.js --group-id <group-id> --names-only');
            console.error('  node list-group-members.js --group-id <group-id> --emails-only');
            process.exit(1);
        }

        // Determine output type
        let outputType = 'full';
        if (options.namesOnly) {
            outputType = 'names-only';
        } else if (options.emailsOnly) {
            outputType = 'emails-only';
        }

        // Get group information
        const groupInfo = await getGroupInfo(options.groupId);
        console.log(`Found group: ${groupInfo.displayName}`);

        // Get group members
        const members = await getGroupMembers(options.groupId);
        
        // Display results
        displayGroupMembers(groupInfo, members, outputType);

    } catch (error) {
        console.error('Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    getGroupMembers,
    getGroupInfo,
    getAuthToken
};
