require("dotenv").config();
const { program } = require("commander");

// Import all existing functions from the repository
const {
  createUserData,
  createUserAccessRequest,
  createUserAccess,
  createUserAccessApproval,
  updateUserAccessRequestEntraIdStatus,
  updateUserAccessRequestStatus,
  getUserData,
  setUserActivationStatus,
} = require("./services/executeQuery.service");

const { sequelize: pgSequelize } = require("./database/postgres/platform-db/models/index");
const { encryptEmail } = require("./services/encryption.service");
const { getInverseDomainMatrixMap } = require("./services/group.service");
const { getEntityIDFromDomain, initDatabase } = require("./services/utils");
const logger = require("./services/logger");
const { v4: uuidv4 } = require("uuid");
const axios = require("axios");
const auth = require("./services/auth");
const constants = require("./config/constants.json");

/**
 * Get Azure AD auth token using existing auth service
 */
async function getAuthToken() {
  let authCacheKey = require("crypto")
    .createHash("md5")
    .update("authCache")
    .digest("hex");
  logger.info(`Generating auth cache key: ${authCacheKey}`);

  let authData = await auth.getToken(auth.tokenRequest);
  let accessToken = authData.accessToken;
  logger.info("Obtained access token");

  return accessToken;
}

/**
 * Check if user exists in Azure AD using existing function pattern
 */
async function checkUserInActiveDirectory(userEmail) {
  try {
    const authToken = await getAuthToken();
    if (!authToken) {
      logger.error("Invalid Entra ID Auth Token");
      throw new Error("Invalid Entra ID Auth Token");
    }

    const options = {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    };

    logger.info(`Checking if user ${userEmail} is part of Active Directory`);
    const response = await axios.get(
      `${process.env.USER_GRAPH_ENDPOINT}/v1.0/users/${userEmail}`,
      options
    );

    if (response.status === 200) {
      logger.info(`User ${userEmail} is part of Active Directory`);
      return response.data;
    }
  } catch (error) {
    if (error.response && error.response.status === 404) {
      logger.info(`User ${userEmail} is not part of Active Directory`);
      return null; // User does not exist
    }
    logger.error(`Error checking user ${userEmail}: ${error.message}`);
    throw error;
  }
}

/**
 * Create user directly in Azure AD
 * Reuses existing patterns from services/graph.js
 */
async function createUserInActiveDirectory(email, displayName, password, accountEnabled = true) {
  try {
    const authToken = await getAuthToken();
    if (!authToken) {
      logger.error("Invalid Entra ID Auth Token");
      throw new Error("Invalid Entra ID Auth Token");
    }

    const options = {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    };

    // Extract mailNickname from email (part before @)
    const mailNickname = email.split("@")[0];
    // Use email as userPrincipalName
    const userPrincipalName = email;

    const data = {
      accountEnabled: accountEnabled,
      displayName: displayName,
      mailNickname: mailNickname,
      userPrincipalName: userPrincipalName,
      passwordProfile: {
        password: password,
        forceChangePasswordNextSignIn: true,
      },
      mail: email,
    };

    logger.info(`Creating user directly in Azure AD: ${email}`);
    const response = await axios.post(
      `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}`,
      data,
      options
    );

    logger.info(
      `[ENTRAIDCREATE] User ${email} has been created in Azure AD with ID: ${response.data.id}`
    );

    return response.data;
  } catch (error) {
    logger.error(`Failed to create user ${email} in Azure AD: ${error.message}`);
    if (error.response) {
      logger.error(`Error response: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

/**
 * Main function to create user directly in Azure AD and database
 * Reuses all existing functions from the repository
 */
async function createUserDirect(options) {
  const {
    email,
    name,
    password,
    role = "USER",
    entityId,
    phoneNumber = "NA",
    designation = null,
    activationFlag = "PENDING",
    accessPermissions = [],
  } = options;

  try {
    // Initialize database using existing function
    await initDatabase();

    // Validate email
    if (!email || !email.includes("@")) {
      throw new Error("Invalid email address provided");
    }

    if (!password) {
      throw new Error("Password is required for direct user creation");
    }

    const emailLower = email.toLowerCase();
    const encryptedEmail = encryptEmail(emailLower);

    // Check if user already exists in database using existing function
    const existingUsers = await getUserData({
      EMAIL: encryptedEmail,
    });

    if (existingUsers.length > 0) {
      const existingUser = existingUsers[0];
      logger.warn(
        `User with email ${email} already exists in database (ID: ${existingUser.ID})`
      );
      return {
        success: false,
        message: "User already exists in database",
        userId: existingUser.ID,
      };
    }

    // Extract entity ID from domain using existing function
    const entityDomain = emailLower.split("@")[1].toLowerCase();
    const userEntityId = entityId || getEntityIDFromDomain(entityDomain);

    if (!userEntityId) {
      logger.warn(`User: ${emailLower} entity ID not found, skipping sync`);
      throw new Error(
        `Entity ID not found for domain: ${entityDomain}. Please provide --entity-id parameter.`
      );
    }

    // Generate user ID
    const userId = uuidv4();

    // Extract name from email if not provided
    const userName = name || emailLower.split("@")[0].replace(/[._-]/g, " ");

    logger.info(`Starting direct user creation for: ${email}`);
    logger.info(`User ID: ${userId}, Entity ID: ${userEntityId}, Role: ${role}`);

    // Step 1: Check if user exists in Azure AD using existing function pattern
    const existingAzureUser = await checkUserInActiveDirectory(emailLower);
    if (existingAzureUser) {
      logger.warn(`User ${emailLower} already exists in Azure AD`);
      // Still create in database but use existing Azure AD user
    } else {
      // Step 2: Create user directly in Azure AD
      await createUserInActiveDirectory(emailLower, userName, password, true);
      logger.info(`✓ User created in Azure AD: ${email}`);
    }

    // Step 3: Create user in database using existing function
    await createUserData(
      userId,
      userName,
      encryptedEmail,
      phoneNumber,
      role,
      userEntityId,
      designation,
      null, // existingUserLinkStatus
      activationFlag,
      0 // ndaStatus
    );
    logger.info(`Created user data for: ${userId}`);

    // Step 4: Update status to indicate Azure AD user is ready
    await setUserActivationStatus(encryptedEmail, "ENTRA_ID_INVITE_SENT");
    logger.info(`User activation status set to ENTRA_ID_INVITE_SENT`);

    // Step 5: Create access request (if access permissions provided)
    let requestId = null;
    if (accessPermissions.length > 0) {
      // Use existing createUserAccessRequest function
      requestId = uuidv4();
      await createUserAccessRequest(requestId, userId);
      logger.info(`Created user access request: ${requestId}`);

      // Step 6: Create user access permissions using existing functions
      const domainMatrixmap = getInverseDomainMatrixMap();
      for (const access of accessPermissions) {
        const accessId = uuidv4();
        // Map domain using inverse domain matrix map (existing function)
        const mappedDomain = domainMatrixmap[access.domain] || access.domain;

        // Use existing createUserAccess function
        await createUserAccess(
          requestId,
          accessId,
          userId,
          mappedDomain,
          access.classification,
          "GRANT"
        );

        // Use existing createUserAccessApproval function
        await createUserAccessApproval(
          uuidv4(),
          accessId,
          access.approvalStatus || "APPROVED",
          null
        );
        logger.info(
          `Create approved access for domain: ${mappedDomain}, classification: ${access.classification}`
        );
      }

      // Step 7: Complete the request using existing functions
      await updateUserAccessRequestEntraIdStatus(requestId, "COMPLETED");
      await updateUserAccessRequestStatus(requestId, "COMPLETED");
      logger.info(`Existing user sync complete for ${userId}`);
    }

    logger.info(`✓ User creation completed successfully for: ${email}`);
    return {
      success: true,
      userId,
      requestId,
      email,
      message: "User created successfully in Azure AD and database",
    };
  } catch (err) {
    logger.error(`Failed to create user ${email}: ${err.message}`);
    logger.error(err.stack);
    throw err;
  }
}

// CLI Setup
program
  .name("create-user-direct-azure")
  .description("Create a user directly in Azure AD and database (no invitation needed)")
  .requiredOption("-e, --email <email>", "User's email address")
  .requiredOption("--password <password>", "Temporary password for Azure AD user")
  .option("-n, --name <name>", "User's full name (default: extracted from email)")
  .option("-r, --role <role>", "User role (USER, PRIMARY_SUPERUSER, SECONDARY_SUPERUSER, DG)", "USER")
  .option("--entity-id <entityId>", "Entity ID (e.g., E49). Auto-detected from email domain if not provided")
  .option("-p, --phone <phone>", "Phone number", "NA")
  .option("-d, --designation <designation>", "Job designation")
  .option("--activation-flag <flag>", "Activation flag (PENDING, ACTIVE)", "PENDING")
  .option("--access <access>", "Access permissions in format: 'domain:classification' (can be used multiple times)")
  .parse(process.argv);

const options = program.opts();

// Parse access permissions if provided
let accessPermissions = [];
if (options.access) {
  const accessArray = Array.isArray(options.access) ? options.access : [options.access];
  accessPermissions = accessArray.map((accessStr) => {
    const [domain, classification, approvalStatus] = accessStr.split(":");
    if (!domain || !classification) {
      throw new Error(
        `Invalid access format: ${accessStr}. Expected format: domain:classification[:approvalStatus]`
      );
    }
    return {
      domain: domain.trim(),
      classification: classification.trim(),
      approvalStatus: approvalStatus ? approvalStatus.trim() : "APPROVED",
    };
  });
}

// Run user creation
createUserDirect({
  email: options.email,
  name: options.name,
  password: options.password,
  role: options.role,
  entityId: options.entityId,
  phoneNumber: options.phone,
  designation: options.designation,
  activationFlag: options.activationFlag,
  accessPermissions,
})
  .then((result) => {
    if (result.success) {
      console.log("\n✅ User created successfully!");
      console.log(`   User ID: ${result.userId}`);
      console.log(`   Email: ${result.email}`);
      console.log(`   Azure AD: User created directly (no invitation needed)`);
      if (result.requestId) {
        console.log(`   Access Request ID: ${result.requestId}`);
      }
      process.exit(0);
    } else {
      console.log(`\n⚠️  ${result.message}`);
      process.exit(1);
    }
  })
  .catch((err) => {
    console.error(`\n❌ Error: ${err.message}`);
    process.exit(1);
  });

