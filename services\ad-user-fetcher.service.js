/**
 * Service for fetching users from Azure AD by groups
 * Extracted and optimized from suncUsers.js
 */

const https = require('https');
const logger = require('./logger');

class ADUserFetcherService {
    constructor() {
        this.clientId = process.env.USER_CLIENT_ID;
        this.clientSecret = process.env.USER_CLIENT_SECRET;
        this.tenantId = process.env.USER_TENANT_ID;
        this.graphEndpoint = process.env.USER_GRAPH_ENDPOINT || 'https://graph.microsoft.com';

        if(this.clientId.includes('safe')){
            this.clientId = this.clientId.split(':')[0];
        }
        if(this.clientSecret.includes('safe')){
            this.clientSecret = this.clientSecret.split(':')[0];
        }
        if(this.tenantId.includes('safe')){
            this.tenantId = this.tenantId.split(':')[0];
        }
        
        if (!this.clientId || !this.clientSecret || !this.tenantId) {
            throw new Error('Missing required environment variables: USER_CLIENT_ID, USER_CLIENT_SECRET, USER_TENANT_ID');
        }
        
        this.accessToken = null;
        this.tokenExpiresAt = 0;
    }

    /**
     * Make HTTP request
     */
    makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            };

            const req = https.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.error?.message || data}`));
                        }
                    } catch (e) {
                        reject(new Error(`Failed to parse JSON: ${data}`));
                    }
                });
            });

            req.on('error', (err) => {
                reject(err);
            });

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    }

    /**
     * Acquire access token using client credentials flow
     */
    async getAccessToken() {
        try {
            // Check if we have a valid cached token
            if (this.accessToken && Date.now() < this.tokenExpiresAt) {
                return this.accessToken;
            }

            const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
            
            const body = new URLSearchParams({
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: `${this.graphEndpoint}/.default`,
                grant_type: 'client_credentials'
            }).toString();

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Content-Length': Buffer.byteLength(body)
                },
                body: body
            };

            const tokenData = await this.makeRequest(tokenUrl, options);
            this.accessToken = tokenData.access_token;

            // Calculate token expiration (subtract 5 minutes for safety)
            const expiresIn = tokenData.expires_in || 3600;
            this.tokenExpiresAt = Date.now() + (expiresIn - 300) * 1000;

            return this.accessToken;
        } catch (error) {
            logger.error(`Error acquiring access token: ${error.message}`);
            throw error;
        }
    }

    /**
     * Make authenticated request to Microsoft Graph API
     */
    async makeGraphRequest(url) {
        try {
            const token = await this.getAccessToken();
            
            const options = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'ConsistencyLevel': 'eventual'
                }
            };

            return await this.makeRequest(url, options);
        } catch (error) {
            logger.error(`Graph API request failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch users from a specific Azure AD group
     */
    async fetchUsersFromGroup(groupId, pageSize = 999) {
        const allUsers = [];
        
        try {
            let nextLink = null;
            
            const baseUrl = `${this.graphEndpoint}/v1.0/groups/${groupId}/members`;
            const params = [
                '$select=id,displayName,mail,userPrincipalName,accountEnabled,proxyAddresses,department,jobTitle,createdDateTime,userType',
                `$top=${pageSize}`,
                '$orderby=displayName'
            ];
            let url = `${baseUrl}?${params.join('&')}`;

            while (true) {
                const currentUrl = nextLink || url;
                const response = await this.makeGraphRequest(currentUrl);

                const members = response.value || [];
                const users = members.filter(member => 
                    member['@odata.type'] === '#microsoft.graph.user' || !member['@odata.type']
                );
                allUsers.push(...users);

                logger.info(`Retrieved ${users.length} users from group ${groupId} (Total: ${allUsers.length})`);

                nextLink = response['@odata.nextLink'];
                if (!nextLink) {
                    break;
                }

                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            return allUsers;
        } catch (error) {
            logger.error(`Error fetching users from group ${groupId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get groups for a specific user by their ID
     */
    async getUserGroupsById(userId) {
        try {
            const url = `${this.graphEndpoint}/v1.0/users/${userId}/memberOf?$select=id,displayName`;
            const response = await this.makeGraphRequest(url);
            return response.value || [];
        } catch (error) {
            logger.error(`Error fetching groups for user ${userId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Extract email aliases from user data
     */
    extractUserAliases(user) {
        const aliases = new Set();

        // Add UPN if available
        if (user.userPrincipalName) {
            aliases.add(user.userPrincipalName.toLowerCase());
        }

        // Extract from proxy addresses
        const proxyAddresses = user.proxyAddresses || [];
        for (const proxy of proxyAddresses) {
            if (typeof proxy === 'string' && (proxy.startsWith('smtp:') || proxy.startsWith('SMTP:'))) {
                const email = proxy.substring(5);
                aliases.add(email.toLowerCase());
            }
        }

        return Array.from(aliases).sort();
    }

    /**
     * Process users from groups and extract all necessary data
     */
    async processGroupUsers(groupIds) {
        try {
            logger.info(`Starting to fetch users from ${groupIds.length} groups`);
            
            const allUsersMap = new Map(); // Use Map to deduplicate users by ID
            const userGroupMap = new Map(); // Map user ID to the groups we're fetching from

            // Fetch users from each group
            for (const groupId of groupIds) {
                try {
                    const users = await this.fetchUsersFromGroup(groupId);
                    
                    for (const user of users) {
                        // Store user data (will overwrite duplicates, keeping latest)
                        allUsersMap.set(user.id, user);
                        
                        // Track which groups we're fetching from that contain this user
                        if (!userGroupMap.has(user.id)) {
                            userGroupMap.set(user.id, []);
                        }
                        userGroupMap.get(user.id).push(groupId);
                    }
                } catch (error) {
                    logger.error(`Failed to fetch users from group ${groupId}: ${error.message}`);
                    // Continue with other groups
                }
            }

            // Process users to extract aliases and groups
            logger.info(`Processing ${allUsersMap.size} unique users to extract aliases and groups`);
            const processedUsers = [];
            
            for (const [userId, user] of allUsersMap.entries()) {
                try {
                    // Get user's groups from AD (all groups the user belongs to)
                    const userGroups = await this.getUserGroupsById(userId);
                    const userGroupIdsFromAD = userGroups.map(group => group.id);
                    
                    // Combine groups we're fetching from with all groups from AD
                    const groupsWeFetchFrom = userGroupMap.get(userId) || [];
                    const allGroupIds = [...new Set([...groupsWeFetchFrom, ...userGroupIdsFromAD])]; // Deduplicate
                    
                    // Extract aliases
                    const aliases = this.extractUserAliases(user);
                    
                    processedUsers.push({
                        id: userId,
                        name: user.displayName || '',
                        email: user.mail || '',
                        accountEnabled: user.accountEnabled !== false,
                        userType: user.userType || null,
                        department: user.department || null,
                        jobTitle: user.jobTitle || null,
                        createdDateTime: user.createdDateTime || null,
                        aliases: aliases,
                        groupIds: allGroupIds
                    });
                } catch (error) {
                    logger.error(`Error processing user ${userId}: ${error.message}`);
                    // Continue with other users
                }
            }

            logger.info(`Successfully processed ${processedUsers.length} users`);
            return processedUsers;
        } catch (error) {
            logger.error(`Error processing group users: ${error.message}`);
            throw error;
        }
    }
}

module.exports = { ADUserFetcherService };

