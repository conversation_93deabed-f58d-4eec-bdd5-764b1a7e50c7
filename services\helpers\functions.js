const groupMatrixInfo = require("../helpers/allGroupMatrix.json");


function assignDrillDownGroup(groupUsers) {
    if (!groupUsers?.length) return;

    groupUsers.forEach(({ name, members, id }) => {
        const groupId = id;
        const groupSplit = name.split('__');
        if (!groupSplit.includes('MATRIX') || (!groupSplit.includes('CONFIDENTIAL') && !groupSplit.includes('SENSITIVE') && !groupSplit.includes('SECRET'))) return [];

        const baseGroup = `MATRIX__${groupSplit[1]}`;
        const openDomainGroup = [`${baseGroup}__OPEN`];
        if (groupSplit.includes('SENSITIVE')) openDomainGroup.push(`${baseGroup}__CONFIDENTIAL`);
        if (groupSplit.includes('SECRET')) openDomainGroup.push(`${baseGroup}__SENSITIVE`);

        const groupIds = openDomainGroup
            .map(group => Object.entries(groupMatrixInfo).find(([_, val]) => val === group)?.[0])
            .filter(Boolean);

        groupIds.forEach((id, index) => {
            const existingGroup = groupUsers.find(x => x.id == id);
            if (existingGroup) {
                existingGroup.members = [...new Set([...existingGroup.members, ...members])];
            } else {
                groupUsers.push({
                    id: groupIds[index],
                    name: openDomainGroup[index],
                    members: groupUsers.find(x => x.id == groupId)?.members || []
                });
            }
        });
    });
    return groupUsers;
}


module.exports = {
    assignDrillDownGroup
}