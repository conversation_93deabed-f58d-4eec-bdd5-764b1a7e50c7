const domainMatrixMap = require("./helpers/domainMatrixMap.json");
const groupMatrix = require("./helpers/groupMatrix.json");

async function getDomainMatrixMap() {
    return domainMatrixMap
  }

function getInverseDomainMatrixMap() {
  return Object.fromEntries(
    Object.entries(domainMatrixMap).map(([key, value]) => [value, key])
  );
}

async function getBayaanGroupMatrix() {
    return groupMatrix
}

function getBayaanExternalGroupMatrix() {
    let externalGroupMatrix = {}
    Object.entries(groupMatrix).forEach(([key, value]) => {
        if (value.startsWith("MATRIX__")) {
            externalGroupMatrix[key] = value
        }
    })
    return externalGroupMatrix
}

module.exports = {   
    getDomainMatrixMap,
    getBayaanGroupMatrix,
    getBayaanExternalGroupMatrix,
    getInverseDomainMatrixMap
};
